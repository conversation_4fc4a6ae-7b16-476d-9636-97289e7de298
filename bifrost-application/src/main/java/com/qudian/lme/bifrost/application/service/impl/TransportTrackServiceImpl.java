package com.qudian.lme.bifrost.application.service.impl;

import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.vo.request.TransportTrackReqVO;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.lme.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.lme.bifrost.application.service.TransportTrackService;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@inheritDoc} {@link TransportTrackService}impl
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
@Service
@Slf4j
public class TransportTrackServiceImpl implements TransportTrackService {

    @Resource
    private OrderTransportMapper orderTransportMapper;
    @Resource
    private TransportTrackWorkerFactory transportTrackWorkerFactory;

    @Value("${transport.track.query.batch.size:10}")
    private Integer batchSize;

    @Override
    public TransportTrackRespVO queryTransportTrack(TransportTrackReqVO reqVO) {
        List<OrderTransportPO> list = orderTransportMapper.queryOrderTransportList(reqVO.getOrderNumberList());
        // 根据转运商分组
        Map<String, List<OrderTransportPO>> carrierOrderMap = list.stream()
            .collect(Collectors.groupingBy(OrderTransportPO::getCarrier));
        List<TransportTrackRespVO.TrackingResult> trackingResults = new ArrayList<>();
        for (Map.Entry<String, List<OrderTransportPO>> entry : carrierOrderMap.entrySet()) {
            String carrier = entry.getKey();
            List<OrderTransportPO> orderTransporterList = entry.getValue();
            BaseTransportTrackWorker worker = transportTrackWorkerFactory.getWorker(carrier);
            Lists.partition(orderTransporterList, batchSize).forEach(
                orderTransportPOs -> queryRemoteTransportTrack(worker, orderTransportPOs, trackingResults, carrier)
            );
        }
        // 将查询失败的放入返回
        List<TransportTrackRespVO.TrackingResult> res = new ArrayList<>();
        Map<String, TransportTrackRespVO.TrackingResult> trackingResultMap = trackingResults.stream()
            .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(),
                (v1, v2) -> v1));
        for (String order : reqVO.getOrderNumberList()) {
            if (trackingResultMap.containsKey(order)) {
                res.add(trackingResultMap.get(order));
            } else {
                res.add(TransportTrackRespVO.TrackingResult.builder()
                    .orderNumber(order)
                    .build());
            }
        }
        return TransportTrackRespVO.builder()
            .trackingResults(res)
            .build();
    }

    private void queryRemoteTransportTrack(BaseTransportTrackWorker worker, List<OrderTransportPO> orderTransporterList, List<TransportTrackRespVO.TrackingResult> trackingResults, String forwarderType) {
        List<String> orderNumbers = orderTransporterList.stream()
            .map(OrderTransportPO::getOrderNumber)
            .collect(Collectors.toList());
        try {
            List<TransportTrackRespVO.TrackingResult> results = worker.provideTransportTrackQuery().apply(orderTransporterList);
            // 设置为统一的转运状态
            results.forEach(result -> result.setStatus(worker.getTransshipmentStatus(result)));
            trackingResults.addAll(results);
        } catch (Exception e) {
            // 下游打过error了，这里打warn
            log.warn("query transport track fail, forwarderType:{}, orderNumbers:{}, exception:", forwarderType, orderNumbers, e);
        }
    }
}
