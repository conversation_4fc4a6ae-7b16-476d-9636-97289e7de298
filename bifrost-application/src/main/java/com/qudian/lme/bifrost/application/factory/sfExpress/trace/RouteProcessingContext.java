package com.qudian.lme.bifrost.application.factory.sfExpress.trace;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto.SfExpressTraceEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>文件名称:com.qudian.lme.bifrost.application.factory.sfExpress.trace.RouteProcessingContext</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class RouteProcessingContext {
    private String originalJSON;
    private SfExpressRouteDTO parsedData;
    private Map<String, Object> metadata;
    private LocalDateTime processTime;
    private List<SfExpressTraceEvent> routeEvents;

    public static RouteProcessingContext create(String json) {
        return new RouteProcessingContext(json, null, Maps.newHashMap(), LocalDateTime.now(), Lists.newArrayList());
    }

    public RouteProcessingContext withParsedData(SfExpressRouteDTO data) {
        this.parsedData = data;
        return this;
    }

    public RouteProcessingContext withMetadata(String key, Object value) {
        metadata.put(key, value);
        return this;
    }
}
