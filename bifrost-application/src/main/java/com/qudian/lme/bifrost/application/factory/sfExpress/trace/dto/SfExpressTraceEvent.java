package com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto;

import com.qudian.lme.bifrost.application.factory.sfExpress.trace.SfExpressOpCodeConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>文件名称:com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto.SfExpressTraceEvent</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SfExpressTraceEvent {
    private String mailno;           // 运单号
    private String opCode;           // 操作码
    private SfExpressOpCodeConfig.SfExpressEventType eventType; // 事件类型
    private String acceptTime;       // 接收时间
    private String acceptAddress;    // 接收地址
    private String remark;           // 备注
    private String orderId;          // 订单ID
}
