package com.qudian.lme.bifrost.application.factory.orderTransport;

import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.ModifyTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.OrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.transport.TransportMessageVO;
import com.qudian.lme.bifrost.application.convertor.OrderTransportStruct;
import com.qudian.lme.bifrost.common.utils.middleware.RedisUtil;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;

import javax.annotation.Resource;
import javax.validation.Valid;

public abstract class OrderTransportHandler {



    @Resource
    protected OrderTransportStruct orderTransportStruct;

    @Resource
    protected OrderTransportMapper orderTransportMapper;

    @Resource
    protected RedisUtil redisUtil;

    public abstract String support();

    public TransportMessageVO<String> dispatchTransport(OrderTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }

    public void cancelTransport(CancelTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }

    public Boolean modifyTransportAddress(ModifyTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }

    public PrintLabelResponseVO printLabelV2(OrderTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }


    /**
     * 转运单个预下单
     * @param reqVO
     * @return
     */
    public PushSingleTransportResponseVO pushSingleTransport(@Valid PushSingleTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }

    /**
     * 打印单个面单(返回面单下载地址)
     * @param reqVO
     * @return
     */
    public PrintLabelResponseVO printSingleLabel(@Valid SingleOrderTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }

    /**
     * 单个确认下单
     *
     * @param reqVO
     * @return
     */
    public String dispatchSingleTransport(@Valid SingleOrderTransportReqVO reqVO) {
        throw new UnsupportedOperationException();
    }
}
