package com.qudian.lme.bifrost.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.qudian.lme.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.lme.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.lme.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.lme.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.lme.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.lme.bifrost.application.service.ExpressService;
import com.qudian.lme.bifrost.application.validation.BizUtil;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.utils.common.StringUtil;
import com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.factory.SfApiHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Service
@Slf4j
public class ExpressServiceImpl implements ExpressService {

    @Resource
    private SfApiHandlerFactory sfApiHandlerFactory;

    @Override
    public CreateExpressRespVO create(CreateExpressReqVO createExpressReqVO) {
        StringUtil.removeSpaces(createExpressReqVO);
        createVerify(createExpressReqVO);
        CreateExpressRespVO createExpressRespVO = (CreateExpressRespVO) sfApiHandlerFactory.getHandler(SfApiCodeEnum.CREATE_ORDER).execute(createExpressReqVO);
        return createExpressRespVO;
    }

    @Override
    public BatchPrintRespVO batchPrint(BatchPrintReqVO batchPrintReqVO) {
        StringUtil.removeSpaces(batchPrintReqVO);
        batchPrintVerify(batchPrintReqVO);
        BatchPrintRespVO batchPrintRespVO = (BatchPrintRespVO) sfApiHandlerFactory.getHandler(SfApiCodeEnum.BATCH_PRINT_WAYBILLS).execute(batchPrintReqVO);
        return batchPrintRespVO;
    }

    private void batchPrintVerify(BatchPrintReqVO batchPrintReqVO) {
        if (CollUtil.isEmpty(batchPrintReqVO.getBatchRequest())) {
            throw new BizException("batchRequest must not be null.");
        }
        if (batchPrintReqVO.getBatchRequest().stream().anyMatch(t -> StrUtil.isBlank(t.getWaybillNo()))) {
            throw new BizException("batchRequest.waybillNo must not be null.");
        }
    }


    private void createVerify(CreateExpressReqVO createExpressReqVO) {
        Optional.ofNullable(createExpressReqVO).orElseThrow(() -> new BizException("createExpressReqVO must not be null."));
        Optional.ofNullable(createExpressReqVO).orElseThrow(() -> new BizException("createExpressReqVO must not be null."));
        BizUtil.requireNonBlank(createExpressReqVO.getOrderNo(), "orderNo must not be null.");
        BizUtil.requireNonNull(createExpressReqVO.getCargoDetails(), "cargoDetails must not be null.");
        if (CollUtil.isEmpty(createExpressReqVO.getCargoDetails())) {
            throw new BizException("cargoDetails must not be null.");
        }
        if (createExpressReqVO.getCargoDetails().stream().anyMatch(t -> StrUtil.isBlank(t.getName()))) {
            throw new BizException("cargoDetails.name must not be null.");
        }

        try {
            if (StrUtil.isNotBlank(createExpressReqVO.getSendStartTm())) {
                DateUtil.parse(createExpressReqVO.getSendStartTm(), DatePattern.NORM_DATETIME_PATTERN);
//                LocalDateTime.parse(createExpressReqVO.getSendStartTm(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        } catch (Exception e) {
            throw new BizException("sendStartTm must be yyyy-MM-dd HH:mm:ss format.");
        }


        BizUtil.requireNonNull(createExpressReqVO.getSendContactInfo(), "sendContactInfo must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getSendContactInfo().getContact(), "sendContactInfo.contact must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getSendContactInfo().getProvince(), "sendContactInfo.province must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getSendContactInfo().getCity(), "sendContactInfo.city must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getSendContactInfo().getAddress(), "sendContactInfo.address must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getSendContactInfo().getMobile(), "sendContactInfo.mobile must not be null.");


        BizUtil.requireNonNull(createExpressReqVO.getDestContactInfo(), "destContactInfo must not be null.");
        BizUtil.requireNonNull(createExpressReqVO.getDestContactInfo().getContact(), "destContactInfo.contact must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getDestContactInfo().getAddress(), "destContactInfo.address must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getDestContactInfo().getProvince(), "destContactInfo.province must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getDestContactInfo().getCity(), "destContactInfo.city must not be null.");
        BizUtil.requireNonBlank(createExpressReqVO.getDestContactInfo().getMobile(), "destContactInfo.mobile must not be null.");
    }


}
