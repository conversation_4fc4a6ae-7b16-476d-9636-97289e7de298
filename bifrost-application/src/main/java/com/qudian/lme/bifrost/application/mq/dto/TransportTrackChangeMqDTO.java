package com.qudian.lme.bifrost.application.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * {@inheritDoc} 物流轨迹变更消息
 *
 * <AUTHOR>
 * @since 2023/8/1
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportTrackChangeMqDTO implements Serializable {

    private static final long serialVersionUID = 7693440374190117302L;

    /**
     * 运单id
     */
    private String orderNumber;
    /**
     * 追踪id
     */
    private String articleId;
    /**
     * 转运物流状态
     */
    private String status;
    /**
     * 服务商类型
     */
    @Deprecated
    private String forwarderType;
    /**
     * 承运商
     */
    private String carrier;
    /**
     * 变更事件
     */
    private List<ChangeEvent> changeEvents;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangeEvent implements Serializable {

        /**
         * 位置
         */
        private String location;
        /**
         * 描述
         */
        private String desc;
        /**
         * 时间
         */
        private String date;
        /**
         * 毫秒时间戳
         */
        private Long eventTime;
        /**
         * 时区
         */
        private String eventTimeZone;
        /**
         * 邮编
         */
        private String eventZIPCode;
        /**
         * 城市
         */
        private String eventCity;
        /**
         * 州
         */
        private String eventState;
        /**
         * 国家
         */
        private String eventCountry;
        /**
         * 公司名称
         */
        private String firmName;

        private String gmtOffset;
    }

}
