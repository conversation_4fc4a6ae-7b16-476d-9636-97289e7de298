package com.qudian.lme.bifrost.application.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.lme.bifrost.api.vo.request.backdoor.BackdoorOrderDeliveredReqVO;
import com.qudian.lme.bifrost.application.mq.dto.TransportDeliveredMqDTO;
import com.qudian.lme.bifrost.application.service.BackdoorService;
import com.qudian.lme.bifrost.common.enums.MqTagEnum;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTraceAnalyzeLogMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.mq.MqProducer;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTraceAnalyzeLogPO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.qudian.lme.bifrost.common.enums.TransportStatusEnum.SHIPMENT_SIGN;

/**
 * @Author: yangxinye
 * @Date: 2023/11/27
 * @Version: 1.0.0
 **/
@Service
@Slf4j
public class BackdoorServiceImpl implements BackdoorService {
    @Resource
    private OrderTransportMapper orderTransportMapper;
    @Resource
    private OrderTraceAnalyzeLogMapper orderTraceAnalyzeLogMapper;
    @Resource
    private MqProducer mqProducer;

    @Override
    public Boolean orderDelivered(BackdoorOrderDeliveredReqVO reqVO) {
        // 如果是强制签收
        Boolean force = reqVO.getForce();
        List<OrderTransportPO> orderTransportPOS = orderTransportMapper.queryOrderTransportList(reqVO.getOrders());

        for (OrderTransportPO orderTransportPO : orderTransportPOS) {

            String orderNumber = orderTransportPO.getOrderNumber();
            log.info("backdoor delivered orderId:{}", orderNumber);
            OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO = orderTraceAnalyzeLogMapper.selectLastOneByOrderNumber(orderNumber);

            TransportDeliveredMqDTO deliveredMqDTO = TransportDeliveredMqDTO.builder()
                .forwarderType(orderTransportPO.getForwarderType())
                .orderNumber(orderNumber)
                .articleId(orderTransportPO.getArticleId())
                .status(TransshipmentStatusEnum.DELIVERED.getCode())
                .carrier(orderTransportPO.getCarrier())
                .date(Optional.ofNullable(orderTraceAnalyzeLogPO).map(OrderTraceAnalyzeLogPO::getAnalyzeTime)
                    .orElse(DateUtil.formatDateTime(orderTransportPO.getUpdatedTime())))
                .build();

            mqProducer.sendEntityMsg(deliveredMqDTO, MqTagEnum.TRANSPORT_DELIVERED.getTag(), orderNumber);
            log.info("backdoor delivered orderId:{}, send msg:{}", orderNumber, JSON.toJSONString(deliveredMqDTO));
            // 更新本地运单表状态
            if (force) {
                log.info("backdoor delivered orderId:{}, update status", orderNumber);
                if (orderTransportPO.getFinishTime() == null) {
                    orderTransportMapper.updateStatusAndFinishTimeById(orderTransportPO.getId(), SHIPMENT_SIGN.getCode(), new Date());
                } else {
                    // 更新本地运单表状态
                    orderTransportMapper.updateStatusById(orderTransportPO.getId(), SHIPMENT_SIGN.getCode());
                }
            }
        }
        return Boolean.TRUE;
    }
}
