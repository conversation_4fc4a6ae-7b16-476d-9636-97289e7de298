package com.qudian.lme.bifrost.application.convertor;

import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.BeforeMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/17
 **/
@Mapper(imports = {
    CarrierTypeEnum.class,
    Optional.class
})
public interface WiseWayTransportTrackStruct {

    @Mappings({
        @Mapping(target = "articleId", source = "dto.trackingNo"),
        @Mapping(target = "carrier", expression = "java(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())"),
        @Mapping(target = "orderNumber", expression = "java(articaleToOrderMap.get(dto.getTrackingNo()))")
    })
    TransportTrackRespVO.TrackingResult toTrackResult(WiseWayOrderTrackRespVO dto, Map<String, String> articaleToOrderMap);

    @Mappings({
        @Mapping(target = "desc", source = "dto.activity"),
        @Mapping(target = "eventCode", source = "dto.eventCode"),
        @Mapping(target = "date", source = "dto.eventTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
        @Mapping(target = "eventTime", expression = "java(dto.getEventTime().getTime())")
    })
    TransportTrackRespVO.TrackingResult.Event toEvent(WiseWayOrderTrackRespVO.Event dto);

    @BeforeMapping
    default void beforeMapping(@MappingTarget TransportTrackRespVO.TrackingResult.TrackingResultBuilder po, WiseWayOrderTrackRespVO dto, Map<String, String> articaleToOrderMap) {
        // 反转事件列表，将最新的事件放到第一
        List<WiseWayOrderTrackRespVO.Event> events = dto.getEvents();
        if (CollectionUtils.isEmpty(events)) {
            return;
        }
        events = Lists.reverse(events);
        // 如果最新事件没有eventCode，取上一个事件的
        WiseWayOrderTrackRespVO.Event newest = events.get(0);
        if (newest.getEventCode() == null && events.size() > 1) {
            newest.setEventCode(events.get(1).getEventCode());
        }
        dto.setEvents(events);
        // 状态改为最新事件的eventCode
        dto.setStatus(events.get(0).getEventCode());
    }

}
