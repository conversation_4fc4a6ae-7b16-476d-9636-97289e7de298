package com.qudian.lme.bifrost.application.factory.orderTransport.impl;

import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.ModifyTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.OrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.lme.bifrost.api.vo.response.transport.TransportMessageVO;
import com.qudian.lme.bifrost.application.convertor.EweRemoteStruct;
import com.qudian.lme.bifrost.application.factory.orderTransport.OrderTransportHandler;
import com.qudian.lme.bifrost.common.enums.ExceptionEnum;
import com.qudian.lme.bifrost.common.enums.TransportStatusEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.infrastructure.repository.remote.EweRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.qudian.lme.bifrost.common.enums.ExceptionEnum.THIRD_SYSTEM_ERROR;

@Component
public class AusOrderTransport extends OrderTransportHandler {

    @Resource
    protected EweRemoteService eweRemoteService;
    @Resource
    protected EweRemoteStruct eweRemoteStruct;

    @Override
    public String support() {
        return CarrierTypeEnum.AUS_POST.getName();
    }

    @Override
    public TransportMessageVO<String> dispatchTransport(OrderTransportReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            throw new BizException("dispatchTransport request param is not empty");
        }
        for (String orderNumber : reqVO.getOrderNumbers()) {
            OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(orderNumber, 0);
            // 没有下单信息 不允许发起转运
            if (Objects.isNull(orderTransportPO)) {
                throw new BizException("this orderNumber not exists".concat(orderNumber));
            }
            // 如果状态不是打印面单状态，不允许发起转运
            if (Objects.equals(orderTransportPO.getStatus(), TransportStatusEnum.PUSH_UNCREATED_SHIPMENTS.getCode())) {
                throw new BizException("please print label：".concat(orderNumber));
            }
            // 已经发起过转运，返回转运重复的消息
            if (orderTransportPO.getStatus() >= TransportStatusEnum.DISPATCH_SHIPMENTS.getCode()) {
                return TransportMessageVO.<String>builder()
                    .code(ExceptionEnum.REPEAT.getCode())
                    .message("Please do not re-initiate transfer : " + orderNumber)
                    .data(null)
                    .build();
            }
        }
        // 澳邮转运
        eweRemoteService.dispatchTransport(reqVO);
        // 记录转运
        orderTransportMapper.updateStatusAndFinishTime(TransportStatusEnum.DISPATCH_SHIPMENTS.getCode(), reqVO.getOrderNumbers(), new Date());
        return TransportMessageVO.<String>builder()
            .code(ExceptionEnum.SUCCESS.getCode())
            .message("transfer initiate success")
            .data(null)
            .build();
    }

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        // 澳邮取消转运
        eweRemoteService.cancelTransport(reqVO);
    }

    @Override
    public Boolean modifyTransportAddress(ModifyTransportReqVO reqVO) {
        // 澳邮修改转运单
        return eweRemoteService.modifyTransportAddress(reqVO);
    }

    @Override
    public PrintLabelResponseVO printLabelV2(OrderTransportReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            throw new BizException("printLabelV2 request param is not empty");
        }
        List<String> orderNumbers = reqVO.getOrderNumbers();
        for (String orderNumber : orderNumbers) {
            OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(orderNumber, 0);
            // 没有转运下单，不允许打印运单
            if (Objects.isNull(orderTransportPO)) {
                throw new BizException("this orderNumber not exists : ".concat(orderNumber));
            }
        }

        // 打印面单
        UploadFileRespVO result = eweRemoteService.printLabelV2(reqVO);
        // 记录转运
        orderTransportMapper.updateStatusOrigin(TransportStatusEnum.PUSH_UNCREATED_SHIPMENTS.getCode(), TransportStatusEnum.PRINT_LABEL.getCode(), reqVO.getOrderNumbers());
        orderTransportMapper.updateObjectKey(result.getData().getObject_key(),
                result.getData().getSigned_url(), reqVO.getOrderNumbers(),null);
        return PrintLabelResponseVO.builder()
            .objectKey(result.getData().getObject_key())
            .signedUrl(result.getData().getSigned_url())
            .expiredAt(result.getData().getExpired_at()).build();
    }

    @Override
    public PushSingleTransportResponseVO pushSingleTransport(PushSingleTransportReqVO reqVO) {

        String orderNumber = reqVO.getShipment().getOrderNumber();
        // ！！！！！！！！！ 请务必注意，传进来的对象 PushSingleTransportReqVO reqVO 和传递给 transportReqVO 的不是同一个 但是里面的字段是相同的，如果有下面用到的，请一定要复制过来
        // 澳邮不支持退件地址，把退件地址设为发件人
        PushTransportReqVO transportReqVO = PushTransportReqVO.builder()
                .forwarderType(ForwarderTypeEnum.EWE.getName())
                .shipments(Lists.newArrayList(eweRemoteStruct.toShipment(reqVO.getShipment())))
                .warehouseLocationState(reqVO.getWarehouseLocationState())
                .channel(reqVO.getChannel())
                .origin(reqVO.getOrigin())
                .build();
        PushTransportResponseVO.Item item = eweRemoteService.pushShipment(transportReqVO).stream()
            // 目前只有一个包裹，直接取第一个
            .findFirst()
            .map(PushTransportResponseVO::getItems)
            .orElse(Collections.emptyList()).stream()
            .findFirst()
            .orElseThrow(() -> new BizException(THIRD_SYSTEM_ERROR));

        return PushSingleTransportResponseVO.builder()
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .articleId(item.getArticleId())
            .orderNumber(orderNumber)
            .consignmentId(item.getConsignmentId())
            .pdfUrl(item.getPdfUrl())
            .state(reqVO.getWarehouseLocationState())
                .origin(reqVO.getOrigin())
            .build();
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        OrderTransportReqVO transportReqVO = new  OrderTransportReqVO();
        transportReqVO.setForwarderType(ForwarderTypeEnum.EWE.getName());
        transportReqVO.setOrderNumbers(Lists.newArrayList(reqVO.getOrderNumber()));
        transportReqVO.setState(reqVO.getState());
        transportReqVO.setChannel(reqVO.getChannel());
        transportReqVO.setOrigin(reqVO.getOrigin());
        // 打印面单
        UploadFileRespVO result = eweRemoteService.printLabelV2(transportReqVO);

        return PrintLabelResponseVO.builder()
            .objectKey(result.getData().getObject_key())
            .signedUrl(result.getData().getSigned_url())
            .expiredAt(result.getData().getExpired_at())
            .build();
    }

    @Override
    public String dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        OrderTransportReqVO transportReqVO = new OrderTransportReqVO();
        transportReqVO.setForwarderType(ForwarderTypeEnum.EWE.getName());
        transportReqVO.setOrderNumbers(Lists.newArrayList(reqVO.getOrderNumber()));
        transportReqVO.setState(reqVO.getState());
        transportReqVO.setChannel(reqVO.getChannel());
        transportReqVO.setOrigin(reqVO.getOrigin());
        // 澳邮转运
        eweRemoteService.dispatchTransport(transportReqVO);
        return "transfer initiate success";
    }


}
