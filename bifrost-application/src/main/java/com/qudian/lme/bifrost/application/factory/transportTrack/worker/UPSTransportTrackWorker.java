package com.qudian.lme.bifrost.application.factory.transportTrack.worker;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.convertor.UPSTransportTrackStruct;
import com.qudian.lme.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.lme.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.lme.bifrost.infrastructure.repository.remote.UPSRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.UPSTransportOrderTraceReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@inheritDoc} USPS轨迹
 *
 * <AUTHOR>
 * @since 2023/9/7
 **/
@Component
public class UPSTransportTrackWorker extends BaseTransportTrackWorker {

    @Resource
    private UPSRemoteService upsRemoteService;
    @Resource
    private UPSTransportTrackStruct upsTransportTrackStruct;

    @Value("${ups.track.partition.size:10}")
    private Integer partitionSize;

    /**
     * USPS状态转换到自定转运状态
     */
    private static final Map<String, TransshipmentStatusEnum> TRANSSHIPMENT_STATUS_MAP = new ImmutableMap.Builder<String, TransshipmentStatusEnum>()
        // 转运待揽收
        .put("pre-shipment", TransshipmentStatusEnum.WAITING_PICKUP)
        // 转运配送中
        .put("moving through network", TransshipmentStatusEnum.IN_TRANSIT)
        // 转运到达待取
        .put("out for delivery", TransshipmentStatusEnum.AWAITING_COLLECTION)
        .put("available for pickup", TransshipmentStatusEnum.AWAITING_COLLECTION)
        // 转运已签收
        .put("delivered", TransshipmentStatusEnum.DELIVERED)
        // 转运异常
        .put("alert", TransshipmentStatusEnum.ABNORMAL)
        .put("delivery attempt", TransshipmentStatusEnum.ABNORMAL)
        .build();

    @Override
    public String support() {
        return CarrierTypeEnum.USPS.getName();
    }

    @PostConstruct
    public void initWorker() {
        TransportTrackWorkerFactory.register(support(), this);
    }

    @Override
    public Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery() {
        // usps查询接口每批10个
        return (orderTransportPOList -> Lists.partition(orderTransportPOList, partitionSize).stream()
            .map(this::getTrackingResults)
            .flatMap(Collection::stream)
            .collect(Collectors.toList()));
    }

    @NotNull
    private List<TransportTrackRespVO.TrackingResult> getTrackingResults(List<OrderTransportPO> orderTransportPOList) {
        Map<String, String> articaleToOrderMap = orderTransportPOList.stream()
            .collect(Collectors.toMap(OrderTransportPO::getArticleId, OrderTransportPO::getOrderNumber,
                (o1, o2) -> o1));
        List<String> orderList = new ArrayList<>(articaleToOrderMap.values());
        List<String> articleIdList = new ArrayList<>(articaleToOrderMap.keySet());
        UPSTransportOrderTraceReqDTO reqDTO = new UPSTransportOrderTraceReqDTO();
        reqDTO.setTrackIdList(articleIdList);
        return upsRemoteService.packageTrack(reqDTO, orderList).stream()
            .map(dto -> upsTransportTrackStruct.toTrackingResult(dto, articaleToOrderMap))
            .collect(Collectors.toList());
    }

    @Override
    public Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap() {
        return (trackingResults ->
            trackingResults.stream()
                // 过滤事件为空的
                .filter(res -> CollectionUtils.isNotEmpty(res.getEvents()))
                .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(), (v1, v2) -> v1))
        );
    }

    @Override
    public String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result) {
        // 兜底返回未知
        return TRANSSHIPMENT_STATUS_MAP.getOrDefault(result.getStatus().toLowerCase(), TransshipmentStatusEnum.UNKNOWN).getCode();
    }

}
