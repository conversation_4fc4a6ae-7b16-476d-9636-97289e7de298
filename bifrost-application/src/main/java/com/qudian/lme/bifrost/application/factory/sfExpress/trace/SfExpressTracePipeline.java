package com.qudian.lme.bifrost.application.factory.sfExpress.trace;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.qudian.lme.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO;
import com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto.SfExpressTraceEvent;
import com.qudian.lme.bifrost.application.mq.dto.TransportDeliveredMqDTO;
import com.qudian.lme.bifrost.common.enums.trace.TraceEventMqTagEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.infrastructure.repository.mq.MqProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>文件名称:com.qudian.lme.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@Slf4j
@Component
public class SfExpressTracePipeline {
    @Resource
    private MqProducer mqProducer;
    @Resource
    private SfExpressOpCodeConfig opCodeConfig;

    /**
     * 顺丰物流轨迹，数据流处理
     *
     * @param routeJSON
     */
    public void flow(String routeJSON) {
        Mono.just(RouteProcessingContext.create(routeJSON))
                .flatMap(this::parseRouteJSON)
                .flatMap(this::insertTraceLog)
                .flatMap(this::parseChangeEvent)
                .flatMap(this::notifyChangeEvent)
                .subscribe(
                        context -> log.info("[sf_trace]物流轨迹处理完成, cost: {} ms, metadata: {}", context.getProcessTime().until(LocalDateTime.now(), java.time.temporal.ChronoUnit.MILLIS), context.getMetadata()),
                        error -> log.error("[sf_trace]物流轨迹流程处理失败: {}", error.getMessage(), error)
                );
    }

    /**
     * 解析轨迹的原始路由
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> parseRouteJSON(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            String originalJSON = context.getOriginalJSON();
            if (StringUtils.isBlank(originalJSON)) {
                context.withMetadata("validationStatus", "FAILED")
                        .withMetadata("errorMessage", "顺丰轨迹数据[JSON]不能为空");
                throw new IllegalArgumentException("顺丰轨迹数据[JSON]不能为空");
            }
            try {
                SfExpressRouteDTO parsed = JSON.parseObject(context.getOriginalJSON(), SfExpressRouteDTO.class);
                // 业务数据验证
                if (CollectionUtil.isEmpty(parsed.getWaybillRoute())) {
                    context.withMetadata("validationStatus", "FAILED")
                            .withMetadata("errorMessage", "顺丰轨迹数据[waybillRoute]不能为空");
                    throw new IllegalArgumentException("顺丰轨迹数据[waybillRoute]不能为空");
                }
                return context.withParsedData(parsed)
                        .withMetadata("validationStatus", "PASSED")
                        .withMetadata("routeCount", parsed.getWaybillRoute().size());

            } catch (Exception e) {
                throw new BizException("[sf_trace]解析顺丰轨迹数据失败: " + Throwables.getStackTraceAsString(e));
            }
        });
    }

    /**
     * 插入轨迹日志
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> insertTraceLog(RouteProcessingContext context) {
        return Mono.fromCallable(() -> context);
    }

    /**
     * 解析运单状态变更事件
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> parseChangeEvent(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            List<SfExpressRouteDTO.SfExpressRouteDetailDTO> routes = context.getParsedData().getWaybillRoute();
            // 解析每个轨迹节点的事件
            List<SfExpressTraceEvent> events = new ArrayList<>();

            for (SfExpressRouteDTO.SfExpressRouteDetailDTO route : routes) {
                String opCode = route.getOpCode();
                Optional<SfExpressOpCodeConfig.SfExpressEventType> eventTypeOpt = opCodeConfig.getEventType(opCode);
                if (!eventTypeOpt.isPresent()) {
                    log.warn("[sf_trace].parseChangeEvent unrecognized opCode: {}", opCode);
                    continue;
                }
                SfExpressOpCodeConfig.SfExpressEventType eventType = eventTypeOpt.get();
                SfExpressTraceEvent event = new SfExpressTraceEvent()
                        .setMailno(route.getMailno())
                        .setOpCode(opCode)
                        .setEventType(eventType)
                        .setAcceptTime(route.getAcceptTime())
                        .setAcceptAddress(route.getAcceptAddress())
                        .setRemark(route.getRemark())
                        .setOrderId(route.getOrderid());
                events.add(event);
            }
            return context.setRouteEvents(events)
                    .withMetadata("eventCount", events.size());
        });
    }

    /**
     * 通知运单状态变更事件
     *
     * @param context
     * @return {@link Mono }<{@link RouteProcessingContext }>
     */
    private Mono<RouteProcessingContext> notifyChangeEvent(RouteProcessingContext context) {
        return Mono.fromCallable(() -> {
            List<SfExpressTraceEvent> events = context.getRouteEvents();
            if (CollectionUtil.isEmpty(events)) {
                log.info("[sf_trace].notifyChangeEvent events is empty, skip notify!");
                return context.withMetadata("mqSentCount", 0);
            }
            int mqSentCount = 0;
            for (SfExpressTraceEvent event : events) {
                if (!event.getEventType().isNeedsDeliveryNotification()) {
                    continue;
                }
                try {
                    // 发送状态变更MQ
                    mqProducer.sendEntityMsg(buildDeliveredMqDTO(event), TraceEventMqTagEnum.DELIVERED.tag, event.getMailno());
                    ++mqSentCount;
                    log.info("[sf_trace]notifyChangeEvent.发送轨迹变更MQ: 运单号={}, 事件类型={}, opCode={}", event.getMailno(), event.getEventType().getDescription(), event.getOpCode());
                } catch (Exception e) {
                    log.error("[sf_trace]notifyChangeEvent.发送轨迹变更MQ失败: 运单号={}, 错误={}", event.getMailno(), e.getMessage(), e);
                }
            }
            return context.withMetadata("mqSentCount", mqSentCount);
        });
    }

    /**
     * 构建签收MQ消息
     */
    private TransportDeliveredMqDTO buildDeliveredMqDTO(SfExpressTraceEvent event) {
        return TransportDeliveredMqDTO.builder()
                .orderNumber(event.getOrderId())
                .articleId(event.getMailno())
                .forwarderType("SF") // 顺丰
                .status(TransshipmentStatusEnum.DELIVERED.getCode())
                .carrier("顺丰速运")
                .date(event.getAcceptTime())
                .eventTime(parseEventTime(event.getAcceptTime()))
                .build();
    }

    /**
     * 解析事件时间为毫秒时间戳
     */
    private Long parseEventTime(String acceptTime) {
        try {
            if (StringUtils.isBlank(acceptTime)) {
                return System.currentTimeMillis();
            }
            // 顺丰时间格式: 2020-05-11 16:56:54
            LocalDateTime dateTime = LocalDateTime.parse(acceptTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            log.warn("[sf_trace]parseEventTime.解析时间失败: {}, 使用当前时间", acceptTime, e);
            return System.currentTimeMillis();
        }
    }

}
