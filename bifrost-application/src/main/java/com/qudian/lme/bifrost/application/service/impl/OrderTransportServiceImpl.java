package com.qudian.lme.bifrost.application.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.vo.PagingList;
import com.qudian.lme.bifrost.api.vo.request.*;
import com.qudian.lme.bifrost.api.vo.response.DeliverySettleListResVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.QuerySupportCarriersRespVO;
import com.qudian.lme.bifrost.application.service.OrderTransportService;
import com.qudian.lme.bifrost.application.service.TransportService;
import com.qudian.lme.bifrost.common.enums.CountryCodeEnum;
import com.qudian.lme.bifrost.infrastructure.international.CountryDataVO;
import com.qudian.lme.bifrost.infrastructure.international.CountryFactory;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OrderTransportServiceImpl extends ServiceImpl<OrderTransportMapper, OrderTransportPO> implements OrderTransportService {
    @Resource
    private TransportService transportService;
    @Resource
    private CountryFactory countryFactory;

    @Override
    public BaseResponseDTO<String> cancelTransport(CancelTransportReqVO reqVO) {
        return transportService.cancelTransport(reqVO);
    }
    
    @Override
    public BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransport(PushSingleTransportReqVO reqVO) {
       return transportService.pushSingleTransport(reqVO);
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        return transportService.printSingleLabel(reqVO);
    }

    @Override
    public BaseResponseDTO<String> dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        return transportService.dispatchSingleTransport(reqVO);
    }

    @Override
    public QuerySupportCarriersRespVO querySupportCarriers(QuerySupportCarriersReqVO reqVO) {
        // 暂时根据国家返回支持的承运商
        CountryDataVO countryDataVO = countryFactory.get();
        return QuerySupportCarriersRespVO.builder()
            .carriers(countryDataVO.getCarrier().getSupportCarriers().stream()
                .map(CarrierTypeEnum::getName)
                .collect(Collectors.toList()))
            .build();
    }

    @Override
    public QuerySupportCarriersRespVO querySupportCarriersByCountry(SupportCarriersByCountryVO reqVO) {
        CountryCodeEnum countryCodeEnum = CountryCodeEnum.getCountryByCode(reqVO.getCountryCode());
;
        CountryDataVO countryDataVO = countryFactory.getCountryDataVO(countryCodeEnum);
        List<CarrierTypeEnum> supportCarriers = countryDataVO.getCarrier().getSupportCarriers();
        List<String> carriers =new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supportCarriers)){
            carriers = supportCarriers.stream().map(CarrierTypeEnum::getName).collect(Collectors.toList());
        }
        return QuerySupportCarriersRespVO.builder()
                .carriers(carriers)
                .build();
    }

    @Override
    public PagingList<DeliverySettleListResVO> querySettleList(DeliverySettleListReqVO reqVO) {
        return transportService.querySettleList(reqVO);
    }

}
