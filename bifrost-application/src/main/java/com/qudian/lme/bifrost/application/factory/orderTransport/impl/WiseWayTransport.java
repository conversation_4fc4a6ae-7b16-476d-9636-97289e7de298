package com.qudian.lme.bifrost.application.factory.orderTransport.impl;

import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.application.factory.orderTransport.OrderTransportHandler;
import com.qudian.lme.bifrost.infrastructure.repository.remote.WiseWayService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class WiseWayTransport extends OrderTransportHandler {

    @Resource
    protected WiseWayService wiseWayService;

    @Override
    public String support() {
        return CarrierTypeEnum.AUS_POST_WISE_WAY.getName();
    }


    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        // 澳邮取消转运
        wiseWayService.cancelTransport(reqVO);
    }


    @Override
    public PushSingleTransportResponseVO pushSingleTransport(PushSingleTransportReqVO reqVO) {


        PushSingleTransportResponseVO pushSingleTransportResponseVO = wiseWayService.pushShipment(reqVO);

        return PushSingleTransportResponseVO.builder()
                .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
                .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
                .articleId(pushSingleTransportResponseVO.getArticleId())
                .orderNumber(pushSingleTransportResponseVO.getOrderNumber())
                .consignmentId(pushSingleTransportResponseVO.getConsignmentId())
                .pdfUrl(pushSingleTransportResponseVO.getPdfUrl())
                .state(reqVO.getWarehouseLocationState())
                .build();
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        // 打印面单
        return wiseWayService.printSingleLabel(reqVO);

    }

    @Override
    public String dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        wiseWayService.dispatchSingleTransport(reqVO);
        return "transfer initiate success";
    }
}
