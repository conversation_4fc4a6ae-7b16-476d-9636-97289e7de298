package com.qudian.lme.bifrost.application.convertor;

import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.common.utils.common.DateTimeUtils;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.UPSTransportOrderTraceRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Map;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/7
 **/
@Mapper(imports = {
    CarrierTypeEnum.class,
    DateTimeUtils.class,
})
public interface UPSTransportTrackStruct {

    @Mappings(value = {
        @Mapping(source = "dto.trackId", target = "articleId"),
        @Mapping(source = "dto.statusCategory", target = "status"),
        @Mapping(source = "dto.trackDetailList", target = "events"),
        @Mapping(expression = "java(CarrierTypeEnum.USPS.getName())", target = "carrier"),
        @Mapping(target = "orderNumber", expression = "java(articaleToOrderMap.get(dto.getTrackId()))")
    })
    TransportTrackRespVO.TrackingResult toTrackingResult(UPSTransportOrderTraceRespDTO dto,  Map<String, String> articaleToOrderMap);

    @Mappings(value = {
        @Mapping(source = "dto.event", target = "desc"),
        @Mapping(expression = "java(dto.getEventTime().getTime())", target = "eventTime"),
        @Mapping(expression = "java(dto.getEventCity() + \" \" + dto.getEventState())", target = "location"),
        @Mapping(source = "dto.eventTime", dateFormat = "yyyy-MM-dd HH:mm:ss", target = "date"),
        @Mapping(expression = "java(DateTimeUtils.parseGMTOffset(dto.getGmtOffset()))", target = "eventTimeZone"),
        @Mapping(constant = "US", target = "eventCountry")
    })
    TransportTrackRespVO.TrackingResult.Event toEvent(UPSTransportOrderTraceRespDTO.TrackDetail dto);

}
