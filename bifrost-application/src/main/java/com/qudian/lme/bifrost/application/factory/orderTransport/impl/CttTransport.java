package com.qudian.lme.bifrost.application.factory.orderTransport.impl;


import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.application.factory.orderTransport.OrderTransportHandler;
import com.qudian.lme.bifrost.infrastructure.repository.remote.CTTService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class CttTransport extends OrderTransportHandler {
    @Resource
    private CTTService cttService;
    @Override
    public String support() {
        return CarrierTypeEnum.CTT.getName();
    }

    @Override
    public String dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        return "transfer initiate success";
    }

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        cttService.cancelTransport(reqVO);
    }
    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        return cttService.printSingleLabel(reqVO);
    }

    @Override
    public PushSingleTransportResponseVO pushSingleTransport(PushSingleTransportReqVO reqVO) {
        PushSingleTransportResponseVO pushSingleTransportResponseVO = cttService.createOrder(reqVO);
        if (Objects.nonNull(pushSingleTransportResponseVO)){
            pushSingleTransportResponseVO.setCarrier(CarrierTypeEnum.CTT.getName());
        }
        return pushSingleTransportResponseVO;
    }
}
