package com.qudian.lme.bifrost.application.convertor;

import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.track51.Track51AusPostResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Map;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/16
 **/
@Mapper(imports = {
        CarrierTypeEnum.class
    }
)
public interface Track51TransportTrackStruct {


    @Mappings({
        @Mapping(target = "orderNumber", expression = "java(articaleToOrderMap.get(dto.getTracking_number()))"),
        @Mapping(target = "status", source = "dto.delivery_status"),
        @Mapping(target = "articleId", source = "dto.tracking_number"),
        @Mapping(target = "events", source = "dto.origin_info.trackinfo"),
        @Mapping(target = "carrier", expression = "java(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())")
    })
    TransportTrackRespVO.TrackingResult toVOResult(Track51AusPostResponseDTO dto,  Map<String, String> articaleToOrderMap);

    @Mappings({
        @Mapping(target = "desc", source = "dto.tracking_detail"),
        @Mapping(target = "date", source = "dto.checkpoint_date", dateFormat = "yyyy-MM-dd hh:mm:ss"),
        @Mapping(target = "eventCountry",constant = "AU"),
        @Mapping(target = "eventTime", expression = "java(dto.getCheckpoint_date().getTime())")
    })
    TransportTrackRespVO.TrackingResult.Event toEvent(Track51AusPostResponseDTO.Trackinfo dto);
}
