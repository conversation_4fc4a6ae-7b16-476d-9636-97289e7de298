package com.qudian.lme.bifrost.application.factory.orderTransport;

import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class OrderTransportFactory {

    @Resource
    private List<OrderTransportHandler> orderTransportHandlers;

    private final Map<String, OrderTransportHandler> ORDER_TRANSPORT_HANDLER_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void initFactory() {
        ORDER_TRANSPORT_HANDLER_MAP.putAll(orderTransportHandlers.stream().collect(Collectors.toMap(OrderTransportHandler::support, Function.identity())));
    }

    public OrderTransportHandler getHandler(String carrier) {
        if (!ORDER_TRANSPORT_HANDLER_MAP.contains<PERSON><PERSON>(carrier)) {
            throw new UnsupportedOperationException("未知的转运方式");
        }
        return ORDER_TRANSPORT_HANDLER_MAP.get(carrier);
    }
}
