package com.qudian.lme.bifrost.application.factory.transportTrack;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.api.enums.OrderTraceAnalyzeLogStatusEnum;
import com.qudian.lme.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.mq.dto.TransportDeliveredMqDTO;
import com.qudian.lme.bifrost.application.mq.dto.TransportTrackChangeMqDTO;
import com.qudian.lme.bifrost.common.enums.MqTagEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTraceAnalyzeLogMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.mq.MqProducer;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTraceAnalyzeLogPO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.qudian.lme.bifrost.common.enums.TransportStatusEnum.SHIPMENT_SIGN;

/**
 * {@inheritDoc} 转运同步处理
 *
 * <AUTHOR>
 * @since 2023/8/8
 **/
@Slf4j
public abstract class BaseTransportTrackWorker {

    @Resource
    private OrderTraceAnalyzeLogMapper analyzeLogMapper;
    @Resource
    private OrderTransportMapper orderTransportMapper;
    @Resource
    private MqProducer mqProducer;

    @Value("${transport.track.query.time.sleep.milliseconds:6000}")
    private Integer timeSleepMilliseconds;

    @Value("${performance.Topic}")
    private String performanceTopic;
    @Value("${track.minus.time:60}")
    private Integer minusMinutes;
    @Value("${track.log.carriers:CTT,GLS}")
    private HashSet<String> trackLogCarriers;
    private static final Map<String, String> topicMap = new HashMap<>();

    @PostConstruct
    public void setTopicMap() {
        topicMap.put("YourBox", performanceTopic);
    }

    public abstract String support();

    /**
     * 轨迹查询器
     */
    public abstract Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery();

    /**
     * 将查询出的轨迹转成map
     *
     * @return
     */
    public abstract Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap();

    public abstract String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result);

    /**
     * 执行轨迹解析任务
     *
     * @param carrier              承运商类型
     * @param orderTransportPOList 解析的运单PO
     * @param orderNumbers         解析的运单id
     * @param analyzeLogPOMap      分析日志
     * @param queryRemoteRetryTime 查询下游重试次数
     */
    public void execute(String carrier,
                        List<OrderTransportPO> orderTransportPOList,
                        List<String> orderNumbers,
                        Map<String, OrderTraceAnalyzeLogPO> analyzeLogPOMap,
                        Integer queryRemoteRetryTime) {

        List<TransportTrackRespVO.TrackingResult> trackingResults = getTrackingResultsWithRetry(orderTransportPOList,
            orderNumbers,
            queryRemoteRetryTime,
            provideTransportTrackQuery());
        // 如果返回为空就下一批
        if (CollectionUtils.isEmpty(trackingResults)) {
            return;
        }
        Map<String, TransportTrackRespVO.TrackingResult> trackingResultMap = provideTrackingResultMap().apply(trackingResults);
        // 如果返回为空就下一批
        if (CollectionUtils.isEmpty(trackingResultMap)) {
            return;
        }
        // 对每个运单解析
        for (OrderTransportPO orderTransportPO : orderTransportPOList) {
            analyzeOrder(orderTransportPO, analyzeLogPOMap, trackingResultMap, carrier);
        }
    }

    /**
     * 解析运单轨迹
     *
     * @param orderTransportPO  本地运单表数据
     * @param analyzeLogPOMap   物流轨迹解析表数据
     * @param trackingResultMap 查询物流信息
     * @param carrierType       转运服务商类型
     */
    private void analyzeOrder(OrderTransportPO orderTransportPO,
                              Map<String, OrderTraceAnalyzeLogPO> analyzeLogPOMap,
                              Map<String, TransportTrackRespVO.TrackingResult> trackingResultMap,
                              String carrierType) {

        String orderNumber = orderTransportPO.getOrderNumber();
        String origin = orderTransportPO.getOrigin();

        LocalDateTime analyzeTime = Optional.ofNullable(analyzeLogPOMap.get(orderNumber))
            .map(OrderTraceAnalyzeLogPO::getAnalyzeTime)
            .map(DateUtil::parseLocalDateTime)
            // 如果之前没有解析记录，取January 1, 1970, 00:00:00
            .orElse(LocalDateTimeUtil.ofUTC(0));

        TransportTrackRespVO.TrackingResult trackingResult = trackingResultMap.get(orderNumber);
        // 如果渠道接口没返回，处理下个运单
        if (Objects.isNull(trackingResult)) {
            return;
        }
        List<TransportTrackChangeMqDTO.ChangeEvent> changEvents = analyzeEvents(trackingResult, analyzeTime, DateUtil.toLocalDateTime(orderTransportPO.getCreatedTime()));
        // 如果订单没有变更，解析下一个
        if (changEvents.isEmpty()) {
            return;
        }
        // 如果有包裹变更，轨迹表插入事件
        String transshipmentStatus = getTransshipmentStatus(trackingResult);
        String forwarderStatus = trackingResult.getStatus();
        String articleId = trackingResult.getArticleId();
        String carrier = trackingResult.getCarrier();
        String forwarderType = orderTransportPO.getForwarderType();
        LocalDateTime utcTime = LocalDateTime.now(ZoneId.of("UTC"));
        OrderTraceAnalyzeLogPO analyzeLogPO = new OrderTraceAnalyzeLogPO()
            .setEvents(JSON.toJSONString(changEvents))
            .setAnalyzeTime(DateUtil.formatLocalDateTime(utcTime))
            .setForwarderType(forwarderType)
            .setOrderNumber(orderNumber)
            .setOrderStatus(forwarderStatus)
            .setCarrier(carrier)
            .setStatus(OrderTraceAnalyzeLogStatusEnum.ANALYZING.getValue());
        analyzeLogMapper.insert(analyzeLogPO);

        if (TransshipmentStatusEnum.DELIVERED.getCode().equals(transshipmentStatus)) {
            // 发签收MQ
            // 取最新事件为基准事件
            TransportTrackRespVO.TrackingResult.Event event = trackingResult.getEvents().get(0);

            TransportDeliveredMqDTO deliveredMqDTO = TransportDeliveredMqDTO.builder()
                .forwarderType(forwarderType)
                .orderNumber(orderNumber)
                .articleId(articleId)
                .status(transshipmentStatus)
                .carrier(carrier)
                .date(event.getDate())
                .eventTime(event.getEventTime())
                .eventTimeZone(event.getEventTimeZone())
                .eventZIPCode(event.getEventZIPCode())
                .eventCity(event.getEventCity())
                .eventState(event.getEventState())
                .eventCountry(event.getEventCountry())
                .firmName(event.getFirmName())
                .gmtOffset(event.getGmtOffset())
                .build();
            if (Objects.nonNull(topicMap.get(origin))) {
                mqProducer.formulateTopicWhenSendMsg(deliveredMqDTO, MqTagEnum.TRANSPORT_DELIVERED.getTag(), orderNumber, topicMap.get(origin));
            } else {
                mqProducer.sendEntityMsg(deliveredMqDTO, MqTagEnum.TRANSPORT_DELIVERED.getTag(), orderNumber);
            }

            if (orderTransportPO.getFinishTime() == null) {
                orderTransportMapper.updateStatusAndFinishTimeById(orderTransportPO.getId(), SHIPMENT_SIGN.getCode(), new Date());
            } else {
                // 更新本地运单表状态
                orderTransportMapper.updateStatusById(orderTransportPO.getId(), SHIPMENT_SIGN.getCode());
            }
        }

        // 发变更消息
        TransportTrackChangeMqDTO transportTrackChangeMqDTO = TransportTrackChangeMqDTO.builder()
            .forwarderType(forwarderType)
            .orderNumber(orderNumber)
            .articleId(articleId)
            .status(transshipmentStatus)
            .carrier(carrier)
            .changeEvents(changEvents)
            .build();
        if (Objects.nonNull(topicMap.get(origin))) {
            mqProducer.formulateTopicWhenSendMsg(transportTrackChangeMqDTO, MqTagEnum.TRANSPORT_TRACK_CHANGE.getTag(), orderNumber, (topicMap.get(origin)));
        } else {
            mqProducer.sendEntityMsg(transportTrackChangeMqDTO, MqTagEnum.TRANSPORT_TRACK_CHANGE.getTag(), orderNumber);
        }

        // 更新数据库
        orderTransportMapper.updateForwarderStatus(orderTransportPO.getId(), forwarderStatus);
        analyzeLogMapper.updateById(new OrderTraceAnalyzeLogPO()
            .setStatus(OrderTraceAnalyzeLogStatusEnum.COMPLETED.getValue())
            .setId(analyzeLogPO.getId()));
    }

    @Nullable
    private List<TransportTrackRespVO.TrackingResult> getTrackingResultsWithRetry(List<OrderTransportPO> orderTransportPOList,
                                                                                  List<String> orderNumbers,
                                                                                  Integer queryRemoteRetryTime,
                                                                                  Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> transportTrackQuery) {
        int retryTime = 0;
        while (retryTime < queryRemoteRetryTime) {
            try {
                List<TransportTrackRespVO.TrackingResult> trackingResults = transportTrackQuery.apply(orderTransportPOList);
                // 等待防止触发限流
                TimeUnit.MILLISECONDS.sleep(timeSleepMilliseconds);
                return trackingResults;
            } catch (BizException e) {
                log.error("query shipping status fail, orderNumbers:{}, exception", orderNumbers, e);
                return null;
            } catch (Exception e) {
                log.error("query shipping status fail, orderNumbers:{}, exception", orderNumbers, e);
                retryTime++;
            }
        }
        return null;
    }

    /**
     * 分析轨迹事件
     */
    private List<TransportTrackChangeMqDTO.ChangeEvent> analyzeEvents(TransportTrackRespVO.TrackingResult trackingResult, LocalDateTime analyzeTime, LocalDateTime createdTime) {
        List<TransportTrackChangeMqDTO.ChangeEvent> changEvents = new ArrayList<>();
        // 创建时间前x分钟
        LocalDateTime minusTimes = createdTime.minusMinutes(minusMinutes);
        // 解析包裹下每个事件
        trackingResult.getEvents().forEach(event -> {
            LocalDateTime date = DateUtil.parseLocalDateTime(event.getDate());
            // 将时间在上次解析之后&下单之后的事件放入变更事件中
            // 时间跟下单时间相等也可以
            // 如果白名单转运商，判断创建时间-30分钟的轨迹也是合法的
            boolean noBeforeCreateTime = !date.isBefore(createdTime);
            boolean isCarrierAndAfterMinusTimes = trackLogCarriers.contains(trackingResult.getCarrier()) && date.isAfter(minusTimes);
            if ((noBeforeCreateTime || isCarrierAndAfterMinusTimes) && date.isAfter(analyzeTime)) {
                TransportTrackChangeMqDTO.ChangeEvent changeEvent = TransportTrackChangeMqDTO.ChangeEvent.builder()
                    .desc(event.getDesc())
                    .location(event.getLocation())
                    .date(event.getDate())
                    .eventTime(event.getEventTime())
                    .eventTimeZone(event.getEventTimeZone())
                    .eventZIPCode(event.getEventZIPCode())
                    .eventCity(event.getEventCity())
                    .eventState(event.getEventState())
                    .eventCountry(event.getEventCountry())
                    .firmName(event.getFirmName())
                    .gmtOffset(event.getGmtOffset())
                    .build();
                changEvents.add(changeEvent);
            }
        });
        return changEvents;
    }

}
