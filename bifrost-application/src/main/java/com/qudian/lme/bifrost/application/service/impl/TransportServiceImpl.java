package com.qudian.lme.bifrost.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.vo.PagingList;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.DeliverySettleListReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.DeliverySettleListResVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.application.convertor.OrderTransportStruct;
import com.qudian.lme.bifrost.application.factory.orderTransport.OrderTransportFactory;
import com.qudian.lme.bifrost.application.service.TransportService;
import com.qudian.lme.bifrost.common.enums.ExceptionEnum;
import com.qudian.lme.bifrost.common.enums.TransportStatusEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.utils.common.Utils;
import com.qudian.lme.bifrost.infrastructure.handler.PageUtils;
import com.qudian.lme.bifrost.infrastructure.international.CountryFactory;
import com.qudian.lme.bifrost.infrastructure.repository.remote.CommonBaseInfoService;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import com.qudian.lme.bifrost.kernel.DistributedLocker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
@Slf4j
public class TransportServiceImpl implements TransportService {

    @Resource
    private OrderTransportMapper orderTransportMapper;
    @Resource
    private OrderTransportFactory orderTransportFactory;

    @Resource
    private OrderTransportStruct orderTransportStruct;

    @Resource
    private CountryFactory countryFactory;

    @Value("${push.order.block.channels:[]}")
    private Set<String> blockChannelSets;
    @Resource
    private CommonBaseInfoService commonBaseInfoService;

    @Override
    public BaseResponseDTO<String> cancelTransport(CancelTransportReqVO reqVO) {
        String orderNumber = reqVO.getOrderNumber();
        OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(orderNumber, null);
        if (Objects.isNull(orderTransportPO)) {
            throw new BizException("order does not exist : ".concat(orderNumber));
        }
        if (Objects.equals(orderTransportPO.getStatus(), TransportStatusEnum.CANCEL_SHIPMENT.getCode())) {
            String errorMessage = "This order cannot be cancel because the label has been cancel : " + orderNumber;

            return buildErrorRepeatResult(null, errorMessage);
        }
        if (orderTransportPO.getStatus() >= TransportStatusEnum.DISPATCH_SHIPMENTS.getCode()) {
            throw new BizException("The Shipment has dispatched!");
        }
        reqVO.setState(orderTransportPO.getState());
        reqVO.setChannel(orderTransportPO.getChannel());
        reqVO.setOrigin(orderTransportPO.getOrigin());
        reqVO.setThirdOrderNumber(orderTransportPO.getConsignmentId());
        // 根据渠道去调用对应的取消接口
        orderTransportFactory.getHandler(orderTransportPO.getCarrier()).cancelTransport(reqVO);
        // 记录取消转运(只能在发起转运之前取消)
        Integer cancelNumber = orderTransportMapper.updateStatusAndDeleteFlag(TransportStatusEnum.CANCEL_SHIPMENT.getCode(), Collections.singletonList(reqVO.getOrderNumber()));
        log.info("取消的数量为：{}", cancelNumber);
        return ResponseBuilder.buildSuccess(null);
    }

    @Override
    @DistributedLocker(key = "#reqVO.shipment.orderNumber")
    public BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransport(PushSingleTransportReqVO reqVO) {
        // 拦截黑名单渠道
        if (Objects.nonNull(reqVO.getChannel()) && blockChannelSets.contains(reqVO.getChannel())) {
            throw new BizException(ExceptionEnum.CHANNEL_NOT_SUPPORT, "order channel:" + reqVO.getChannel() + " not support");
        }
        String orderNumber = reqVO.getShipment().getOrderNumber();
        OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(orderNumber, 0);
        // 如果已经存在转运单, 直接返回当前的转运单信息
        if (Objects.nonNull(orderTransportPO)) {
            PushSingleTransportResponseVO pushSingleTransportResponseVO = PushSingleTransportResponseVO.builder()
                    .articleId(orderTransportPO.getArticleId())
                    .consignmentId(orderTransportPO.getConsignmentId())
                    .build();
            String errorMessage = orderNumber + " this referenceNo exists, please try another one！";
            return buildErrorRepeatResult(pushSingleTransportResponseVO, errorMessage);
        }
        // 设置转运商
        String carrier = commonBaseInfoService.choiceChannel(reqVO.getChannel(),reqVO.getOrigin(),reqVO.getCarrier());
        // 地址过滤表情符号
        addressIgnoreExpression(reqVO.getShipment());
        PushSingleTransportResponseVO pushSingleTransportResponseVO = orderTransportFactory.getHandler(carrier).pushSingleTransport(reqVO);

        OrderTransportPO po = orderTransportStruct.toPO(pushSingleTransportResponseVO);
        orderTransportMapper.insert(po);
        return ResponseBuilder.buildSuccess(pushSingleTransportResponseVO);

    }

    private static void addressIgnoreExpression(PushSingleTransportReqVO.Shipment shipment) {
        PushSingleTransportReqVO.SenderInfo senderInfo = shipment.getSenderInfo();
        PushSingleTransportReqVO.RecipientInfo recipientInfo = shipment.getRecipientInfo();
        PushSingleTransportReqVO.ReturneeInfo returneeInfo = shipment.getReturneeInfo();
        if (Objects.nonNull(senderInfo))senderInfo.setSenderAddress(Utils.ignoreExpression(senderInfo.getSenderAddress()));
        if (Objects.nonNull(recipientInfo))recipientInfo.setRecipientAddress(Utils.ignoreExpression(recipientInfo.getRecipientAddress()));
        if (Objects.nonNull(returneeInfo))returneeInfo.setReturneeAddress(Utils.ignoreExpression(returneeInfo.getReturneeAddress()));
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            throw new BizException("printLabelV2 request param is not empty");
        }
        String orderNumber = reqVO.getOrderNumber();
        OrderTransportPO orderTransportPO = verifyExistOrderTransport(orderNumber);
        // 没有转运下单，不允许打印运单
        // 已经下载过转运单，直接返回
        if (StringUtils.isNotBlank(orderTransportPO.getObjectKey())){
            return new PrintLabelResponseVO().setObjectKey((orderTransportPO.getObjectKey()));
        }
        // 构建请求参数 建议后来人使用一个新的对象 不要这么构建，很呆
        fillOrderInfo(reqVO, orderTransportPO);
        // 打印面单
        PrintLabelResponseVO printLabelResponseVO = orderTransportFactory.getHandler(orderTransportPO.getCarrier()).printSingleLabel(reqVO);
        // 记录转运
        orderTransportMapper.updateStatusOrigin(TransportStatusEnum.PUSH_UNCREATED_SHIPMENTS.getCode(), TransportStatusEnum.PRINT_LABEL.getCode(), Collections.singletonList(orderNumber));
        orderTransportMapper.updateObjectKey(printLabelResponseVO.getObjectKey(),
                printLabelResponseVO.getSignedUrl(), Collections.singletonList(orderNumber), printLabelResponseVO.getPdfUrl());
        return printLabelResponseVO;
    }

    private static void fillOrderInfo(SingleOrderTransportReqVO reqVO, OrderTransportPO orderTransportPO) {
        reqVO.setState(orderTransportPO.getState());
        reqVO.setChannel(orderTransportPO.getChannel());
        reqVO.setOrigin(orderTransportPO.getOrigin());
        reqVO.setThirdOrderNumber(orderTransportPO.getConsignmentId());
    }

    @NotNull
    private OrderTransportPO verifyExistOrderTransport(String orderNumber) {
        OrderTransportPO orderTransportPO = orderTransportMapper.queryOrderTransportOne(orderNumber, 0);
        // 没有转运下单，不允许打印运单
        if (Objects.isNull(orderTransportPO)) {
            throw new BizException("this orderNumber not exists : ".concat(orderNumber));
        }
        return orderTransportPO;
    }

    @Override
    public BaseResponseDTO<String> dispatchSingleTransport(SingleOrderTransportReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            throw new BizException("dispatchTransport request param is not empty");
        }
        String orderNumber = reqVO.getOrderNumber();
        LambdaQueryWrapper<OrderTransportPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderTransportPO::getDeleteFlag,0).eq(OrderTransportPO::getOrderNumber,orderNumber);
        List<OrderTransportPO> orderTransportPOList = orderTransportMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orderTransportPOList)) {
            throw new BizException("this orderNumber not exists".concat(orderNumber));
        }
        if (orderTransportPOList.size() > 1) {
            throw new BizException(String.format("There is a duplicate order number %s,please contact the operation", orderNumber));
        }
        OrderTransportPO orderTransportPO = orderTransportPOList.get(0);
        // 如果状态不是打印面单状态，不允许发起转运
        if (Objects.equals(orderTransportPO.getStatus(), TransportStatusEnum.PUSH_UNCREATED_SHIPMENTS.getCode())) {
            throw new BizException("please print label：".concat(orderNumber));
        }
        // 已经发起过转运，返回转运重复的消息
        if (orderTransportPO.getStatus() >= TransportStatusEnum.DISPATCH_SHIPMENTS.getCode()) {
            return buildErrorRepeatResult(null, "Please do not re-initiate transfer : " + orderNumber);
        }
        reqVO.setState(orderTransportPO.getState());
        reqVO.setOrigin(orderTransportPO.getOrigin());
        reqVO.setChannel(orderTransportPO.getChannel());
        String res = orderTransportFactory.getHandler(orderTransportPO.getCarrier()).dispatchSingleTransport(reqVO);
        orderTransportMapper.updateStatusAndFinishTime(TransportStatusEnum.DISPATCH_SHIPMENTS.getCode(), Collections.singletonList(reqVO.getOrderNumber()), new Date());
        return ResponseBuilder.buildSuccess(res);
    }

    @NotNull
    private static <T> BaseResponseDTO<T> buildErrorRepeatResult(T data, String message) {
        BaseResponseDTO<T> pushSingleTransportResponseVOBaseResponseDTO = ResponseBuilder.buildSuccess(data);
        pushSingleTransportResponseVOBaseResponseDTO.setCode(ExceptionEnum.REPEAT.getCode());

        pushSingleTransportResponseVOBaseResponseDTO.setMessage(message);
        return pushSingleTransportResponseVOBaseResponseDTO;
    }

    @NotNull
    private static <T> BaseResponseDTO<T> buildErrorChanelBlockResult(T data, String message) {
        BaseResponseDTO<T> pushSingleTransportResponseVOBaseResponseDTO = ResponseBuilder.buildSuccess(data);
        pushSingleTransportResponseVOBaseResponseDTO.setCode(ExceptionEnum.CHANNEL_NOT_SUPPORT.getCode());

        pushSingleTransportResponseVOBaseResponseDTO.setMessage(message);
        return pushSingleTransportResponseVOBaseResponseDTO;
    }

    @Override
    public PagingList<DeliverySettleListResVO> querySettleList(DeliverySettleListReqVO reqVO) {
        PageInfo<DeliverySettleListResVO> pageInfo = PageUtils.getPageInfo(reqVO, () -> orderTransportMapper.querySettleList(reqVO));
        PagingList pagingList = PageUtils.getPagingList(pageInfo);
        return pagingList;
    }

}

