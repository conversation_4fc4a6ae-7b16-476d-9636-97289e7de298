package com.qudian.lme.bifrost.application.factory.transportTrack.worker;

import com.google.common.collect.ImmutableMap;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.TransshipmentStatusEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.lme.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.lme.bifrost.infrastructure.repository.remote.GlsService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 西班牙转运轨迹
 *
 * @Author: yangxinye
 * @Date: 2024/4/11
 * @Version: 1.0.0
 **/
@Component
@Slf4j
public class GlsTransportTrackWorker extends BaseTransportTrackWorker {

    @Resource
    private GlsService glsService;

    /**
     * gls物流状态转换为自定义状态
     * @link <a href="http://wiki.qudian.com/pages/viewpage.action?pageId=85075183">...</a>
     */
    private static final Map<String, TransshipmentStatusEnum> TRANSSHIPMENT_STATUS_MAP = new ImmutableMap.Builder<String, TransshipmentStatusEnum>()
        // 转运待揽收
        .put("-10", TransshipmentStatusEnum.WAITING_PICKUP)
        // 转运配送中
        .put("0", TransshipmentStatusEnum.IN_TRANSIT)
        .put("2", TransshipmentStatusEnum.IN_TRANSIT)
        .put("19", TransshipmentStatusEnum.IN_TRANSIT)
        .put("3", TransshipmentStatusEnum.IN_TRANSIT)
        .put("9", TransshipmentStatusEnum.IN_TRANSIT)
        .put("21", TransshipmentStatusEnum.IN_TRANSIT)
        .put("18", TransshipmentStatusEnum.IN_TRANSIT)
        .put("14", TransshipmentStatusEnum.IN_TRANSIT)
        .put("6", TransshipmentStatusEnum.IN_TRANSIT)
        // 转运到达待取
        .put("22", TransshipmentStatusEnum.AWAITING_COLLECTION)
        .put("25", TransshipmentStatusEnum.AWAITING_COLLECTION)

        // 转运已签收
        .put("7", TransshipmentStatusEnum.DELIVERED)
        .put("8", TransshipmentStatusEnum.DELIVERED)
        // 转运异常
        .put("15", TransshipmentStatusEnum.ABNORMAL)
        .put("10", TransshipmentStatusEnum.ABNORMAL)
        .put("12", TransshipmentStatusEnum.ABNORMAL)
        .put("17", TransshipmentStatusEnum.ABNORMAL)
        .put("5", TransshipmentStatusEnum.ABNORMAL)
        .put("20", TransshipmentStatusEnum.ABNORMAL)
        .put("90", TransshipmentStatusEnum.ABNORMAL)
        .build();


    @PostConstruct
    public void initWorker() {
        TransportTrackWorkerFactory.register(support(), this);
    }

    @Override
    public String support() {
        return CarrierTypeEnum.GLS.getName();
    }

    @Override
    public Function<List<OrderTransportPO>, List<TransportTrackRespVO.TrackingResult>> provideTransportTrackQuery() {
        return this::queryTrack;
    }

    @Override
    public Function<List<TransportTrackRespVO.TrackingResult>, Map<String, TransportTrackRespVO.TrackingResult>> provideTrackingResultMap() {
        return (trackingResults ->
            trackingResults.stream()
                // 过滤事件为空的
                .filter(res -> CollectionUtils.isNotEmpty(res.getEvents()))
                .collect(Collectors.toMap(TransportTrackRespVO.TrackingResult::getOrderNumber, Function.identity(), (v1, v2) -> v1)));
    }

    @Override
    public String getTransshipmentStatus(TransportTrackRespVO.TrackingResult result) {
        return TRANSSHIPMENT_STATUS_MAP.getOrDefault(result.getStatus(), TransshipmentStatusEnum.UNKNOWN).getCode();
    }

    private List<TransportTrackRespVO.TrackingResult> queryTrack(List<OrderTransportPO> orderTransportPOList) {
        List<ShippingStatusReqDTO.ShippingOrderInfo> collect = orderTransportPOList.stream()
            .map(o -> new ShippingStatusReqDTO.ShippingOrderInfo()
                .setOrderId(o.getOrderNumber())
                .setTrackingId(o.getArticleId()))
            .collect(Collectors.toList());

        ShippingStatusReqDTO reqDTO = new ShippingStatusReqDTO()
            .setOrderInfoList(collect);
        TransportTrackRespVO transportTrackRespVO = glsService.shippingStatus(reqDTO);
        return transportTrackRespVO.getTrackingResults();
    }


}
