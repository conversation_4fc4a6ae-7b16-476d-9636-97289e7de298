package com.qudian.lme.bifrost.application.convertor;

import cn.hutool.core.collection.CollectionUtil;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushTransportResponseVO;
import com.qudian.lme.bifrost.common.enums.TransportStatusEnum;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(imports = {TransportStatusEnum.class})
public interface OrderTransportStruct {

    @Mapping(source = "vo.senderReferences", target = "orderNumber")
    @Mapping(expression = "java(TransportStatusEnum.PUSH_UNCREATED_SHIPMENTS.getCode())", target = "status")
    @Mapping(constant = "0", target = "deleteFlag")
    OrderTransportPO toPO(PushTransportResponseVO vo);

    List<OrderTransportPO> toPO(List<PushTransportResponseVO> vo);

    @Mapping(expression = "java(TransportStatusEnum.PUSH_UNCREATED_SHIPMENTS.getCode())", target = "status")
    @Mapping(constant = "0", target = "deleteFlag")
    OrderTransportPO toPO(PushSingleTransportResponseVO pushSingleTransportResponseVO);

    @AfterMapping
    default void afterMapping(@MappingTarget OrderTransportPO.OrderTransportPOBuilder po, PushTransportResponseVO vo){
        List<PushTransportResponseVO.Item> items = vo.getItems();
        if (CollectionUtil.isNotEmpty(items)) {
            PushTransportResponseVO.Item item = items.get(0);
            po.articleId(item.getArticleId());
            po.consignmentId(item.getConsignmentId());
            po.pdfUrl(item.getPdfUrl());
            po.forwarderType(vo.getForwarderType());
        }
    }

}
