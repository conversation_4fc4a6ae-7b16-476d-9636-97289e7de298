package com.qudian.lme.bifrost.application.service;

import com.qudian.lme.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.lme.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.lme.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.lme.bifrost.api.vo.response.express.CreateExpressRespVO;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
public interface ExpressService {
    CreateExpressRespVO create(CreateExpressReqVO createExpressReqVO);

    BatchPrintRespVO batchPrint(BatchPrintReqVO batchPrintReqVO);
}
