package com.qudian.lme.bifrost.application.convertor;

import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushTransportReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/7
 **/
@Mapper
public interface EweRemoteStruct {

    /**
     * 澳邮不支持退件地址，把退件地址设为发件人
     */
    @Mappings(
        value = {
            @Mapping(source = "dto.returneeInfo", target = "senderInformation"),
            @Mapping(source = "dto.recipientInfo", target = "recipientInformation"),
            @Mapping(source = "dto.itemInfo", target = "itemInformation"),
        }
    )
    PushTransportReqVO.Shipment toShipment(PushSingleTransportReqVO.Shipment dto);

    @Mappings(
        value = {
            @Mapping(source = "dto.returnee", target = "sender"),
            @Mapping(source = "dto.returneeMobilePhone", target = "senderMobilePhone"),
            @Mapping(source = "dto.returneeAddress", target = "senderAddress"),
            @Mapping(source = "dto.returneeCountry", target = "senderCountry"),
            @Mapping(source = "dto.returneeProvince", target = "senderProvince"),
            @Mapping(source = "dto.returneeCity", target = "senderCity"),
            @Mapping(source = "dto.returneePostcode", target = "senderPostcode"),
        }
    )
    PushTransportReqVO.SenderInformation toSender(PushSingleTransportReqVO.ReturneeInfo dto);

    @Mappings(
        value = {
            @Mapping(source = "dto.recipient", target = "to"),
        }
    )
    PushTransportReqVO.RecipientInformation toRecipient(PushSingleTransportReqVO.RecipientInfo dto);

}
