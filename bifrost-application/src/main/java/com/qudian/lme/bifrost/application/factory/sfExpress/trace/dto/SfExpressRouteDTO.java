package com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>文件名称:com.qudian.lme.bifrost.application.factory.sfExpress.trace.dto.SfExpressRouteDTO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
/*{
    "Body": {
        "WaybillRoute": [{
                "mailno": "SF7444400031887",
                "acceptAddress": "深圳市",
                "reasonName": "",
                "orderid": "202003225d33322239ddW1df5t3",
                "acceptTime": "2020-05-11 16:56:54",
                "remark": "顺丰速运 已收取快件",
                "opCode": "50",
                "id": "158918741444476",
                "reasonCode": ""
            },
            {
                "mailno": "SF7444400031887",
                "acceptAddress": "郑州市",
                "reasonName": "",
                "orderid": "202003225d33322239ddW1df5t3",
                "acceptTime": "2020-05-11 16:56:54",
                "remark": "快件到达 【郑州园博中转场】",
                "opCode": "31",
                "id": "158918741457126",
                "reasonCode": ""
            }]
    }
}*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SfExpressRouteDTO {
    private List<SfExpressRouteDetailDTO> WaybillRoute;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SfExpressRouteDetailDTO {
        private String mailno;
        private String acceptAddress;
        private String reasonName;
        private String orderid;
        private String acceptTime;
        private String remark;
        private String opCode;
        private String id;
        private String reasonCode;
    }
}
