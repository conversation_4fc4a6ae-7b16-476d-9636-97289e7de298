package com.qudian.lme.bifrost.api.vo.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ExpressDeliveryOrderInfo implements Serializable {
    // 原始单号
    private String transportNo;
    // 第三方单号
    private String transportLabelNo;

    /**
     * 物流商编号
     */
    private String courierCode;


    /**
     * 特殊参数，postCode
     */
    private String trackingPostalCode;
}
