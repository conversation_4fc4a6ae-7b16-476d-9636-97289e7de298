package com.qudian.lme.bifrost.api.vo.request.express;

import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BatchPrintReqVO extends BaseRequestVO {
    private List<BatchPrintReq> batchRequest;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BatchPrintReq{
        private  String waybillNo;
    }


}
