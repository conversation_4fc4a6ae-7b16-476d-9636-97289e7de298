package com.qudian.lme.bifrost.api.facade;

import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.vo.PagingList;
import com.qudian.lme.bifrost.api.vo.request.*;
import com.qudian.lme.bifrost.api.vo.response.*;
import javax.validation.Valid;

public interface OrderTransportFacade {

    /**
     * 运单取消
     *
     * @param reqVO
     * @return
     */
    BaseResponseDTO<String> cancelTransport(@Valid CancelTransportReqVO reqVO);

    /**
     * 转运单个预下单
     * @param reqVO
     * @return
     */
    BaseResponseDTO<PushSingleTransportResponseVO> pushSingleTransport(@Valid PushSingleTransportReqVO reqVO);

    /**
     * 打印单个面单(返回面单下载地址)
     * @param reqVO
     * @return
     */
    BaseResponseDTO<PrintLabelResponseVO> printSingleLabel(@Valid SingleOrderTransportReqVO reqVO);

    /**
     * 单个确认下单
     * @param reqVO
     * @return
     */
    BaseResponseDTO<String> dispatchSingleTransport(@Valid SingleOrderTransportReqVO reqVO);

    /**
     * 查询支持的承运商
     *
     * @param reqVO
     * @return
     */
    BaseResponseDTO<QuerySupportCarriersRespVO> querySupportCarriers(@Valid QuerySupportCarriersReqVO reqVO);

    /**
     * 根据code查询承运商
     * @param reqVO
     * @return
     */
    BaseResponseDTO<QuerySupportCarriersRespVO> querySupportCarriersByCountry(@Valid SupportCarriersByCountryVO reqVO);

    /**
     * 查询订单对账/取数/补数
     * @param reqVO
     * @return
     */
    BaseResponseDTO<PagingList<DeliverySettleListResVO>> queryChannelSettleRecordList(DeliverySettleListReqVO reqVO);
}
