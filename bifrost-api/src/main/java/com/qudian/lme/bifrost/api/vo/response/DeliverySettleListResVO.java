package com.qudian.lme.bifrost.api.vo.response;

import com.qudian.lme.bifrost.api.enums.TransportStatusEnum;
import com.qudian.lme.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Title:  DeliverySettleListResVO.java
 * @Package com.qudian.lme.bifrost.api.vo.response
 * @Description:  $DESC
 * @Author: zhouqingyang
 * @Date:   2023/12/19 19:55
 * @Version V1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverySettleListResVO extends BaseResponseVO {

    private static final long serialVersionUID = -4873247173845653490L;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 状态
     */
    private TransportStatusEnum status;

    /**
     * 终态时间
     */
    private Date finishTime;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 主键id标识
     */
    private Long id;
}
