package com.qudian.lme.bifrost.api.vo.request.express;

import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import com.sun.corba.se.pept.transport.ContactInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CreateExpressReqVO extends BaseRequestVO {

    private String orderNo;
    private String sendStartTm;
    private List<CargoDetail> cargoDetails;
    private ContactInfo sendContactInfo;
    private ContactInfo destContactInfo;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class  CargoDetail{
        private String name;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static  class  ContactInfo{
        private String contact;
        private String address;
        private String mobile;
        private String city;
        private String province;

    }

}
