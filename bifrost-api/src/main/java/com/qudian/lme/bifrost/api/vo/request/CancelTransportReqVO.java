package com.qudian.lme.bifrost.api.vo.request;

import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CancelTransportReqVO extends BaseRequestVO {
    /**
     * 选择的承运商，不传会使用兜底策略
     */
    private String carrier;
    @NotBlank(message = "orderNumber not null")
    private String orderNumber;
    @NotBlank(message = "reason is empty")
    private String reason;

    private String state;


    private String channel;

    private String origin;
    
    private String thirdOrderNumber;
}
