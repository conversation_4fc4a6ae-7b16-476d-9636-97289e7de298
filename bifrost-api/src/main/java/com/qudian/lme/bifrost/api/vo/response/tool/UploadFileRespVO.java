package com.qudian.lme.bifrost.api.vo.response.tool;

import com.qudian.java.components.base.vo.BaseResponseVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>文件名称:com.qudian.lme.driver.api.vo.response.staff.register.UploadFileRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/11/3
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UploadFileRespVO extends BaseResponseVo {
    private static final long serialVersionUID = 8224539587908456665L;
    private String request_uri;

    private FileData data;

    @Data
    public static class FileData extends BaseResponseVo {
        private static final long serialVersionUID = 3896657294830646502L;

        private String object_key;
        private String signed_url;
        private String expired_at;
    }
}
