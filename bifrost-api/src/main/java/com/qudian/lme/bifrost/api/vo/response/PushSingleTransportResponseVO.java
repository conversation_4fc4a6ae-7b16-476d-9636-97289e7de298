package com.qudian.lme.bifrost.api.vo.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.qudian.lme.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/6
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushSingleTransportResponseVO extends BaseResponseVO {

    /**
     * 全局订单号
     */
    private String orderNumber;

    /**
     * 转运服务商类型
     */
    private String forwarderType;
    /**
     * 实际承运商
     */
    private String carrier;
    /**
     * 追踪号
     */
    private String articleId;
    /**
     * 下游生成的订单
     */
    private String consignmentId;
    /**
     * 运单PDF地址
     */
    private String pdfUrl;

    /**
     * 州
     */
    private String state;

    private String origin;

}
