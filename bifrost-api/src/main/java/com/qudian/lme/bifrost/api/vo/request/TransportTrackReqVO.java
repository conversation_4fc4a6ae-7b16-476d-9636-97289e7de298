package com.qudian.lme.bifrost.api.vo.request;

import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Optional;

/**
 * {@inheritDoc} 主动查询物流轨迹VO
 *
 * <AUTHOR>
 * @since 2023/7/28
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransportTrackReqVO extends BaseRequestVO {

    /**
     * 查询物流轨迹的订单号
     */
    @NotEmpty
    private List<String> orderNumberList;

    private String state;

    private Optional<String> channel;

    private Optional<String> origin;
}
