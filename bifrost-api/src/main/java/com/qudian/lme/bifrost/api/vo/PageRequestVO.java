package com.qudian.lme.bifrost.api.vo;

import lombok.*;

import javax.validation.constraints.Max;
import java.util.UUID;

/**
 * <p>文件名称:com.qudian.lme.driver.api.vo.BasePageRequestVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class PageRequestVO extends BaseRequestVO{
    private static final long serialVersionUID = 6355074296556378726L;

    private Integer pageNum = 1;
    @Max(200)
    private Integer pageSize = 10;

    public PageRequestVO(Long userId, Integer pageNum, Integer pageSize) {
        super(userId, UUID.randomUUID().toString());
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }
}
