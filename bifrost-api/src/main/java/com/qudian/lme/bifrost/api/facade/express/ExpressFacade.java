package com.qudian.lme.bifrost.api.facade.express;

import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.lme.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.lme.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.lme.bifrost.api.vo.response.express.CreateExpressRespVO;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
public interface ExpressFacade {

    /**
     * 下单
     * @param createExpressReqVO
     * @return
     */
    BaseResponseDTO<CreateExpressRespVO> create(CreateExpressReqVO createExpressReqVO);

    BaseResponseDTO<BatchPrintRespVO> batchPrint(BatchPrintReqVO batchPrintReqVO);



}
