package com.qudian.lme.bifrost.api.vo.request;

import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Deprecated
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushTransportReqVO extends BaseRequestVO {

    private String forwarderType = ForwarderTypeEnum.EWE.getName();
    @Valid
    private List<Shipment> shipments;

    private String warehouseLocationState;

    private String channel;

    private String origin;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Shipment implements Serializable {
        private SenderInformation senderInformation;
        private RecipientInformation recipientInformation;
        private List<ItemInformation> itemInformation;
        @NotBlank(message = "order is null")
        private String orderNumber;
        private String orderType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SenderInformation implements Serializable {
        private String sender;
        private String senderMobilePhone;
        private String senderAddress;
        private String senderCountry;
        private String senderProvince;
        private String senderCity;
        private String senderPostcode;
        private String county;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecipientInformation implements Serializable {
        private String to;
        private String recipientMobilePhone;
        private String recipientAddress;
        private String receiptCountry;
        private String receiptProvince;
        private String receiptCity;
        private String receiptPostcode;
        private String county;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemInformation implements Serializable {
        private Double weight;
        private Double width;
        private Double length;
        private Double height;
        private String itemDescription;
    }
}
