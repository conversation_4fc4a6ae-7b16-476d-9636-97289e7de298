package com.qudian.lme.bifrost.api.facade;

import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.vo.request.TransportTrackReqVO;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;

import javax.validation.Valid;

/**
 * {@inheritDoc} 转运轨迹追踪
 *
 * <AUTHOR>
 * @since 2023/7/28
 **/
public interface TransportTrackFacade {

    /**
     * 主动查询转运物流轨迹
     * 注意下游渠道商限流
     */
    BaseResponseDTO<TransportTrackRespVO> queryTransportTrack(@Valid TransportTrackReqVO reqVO);

}
