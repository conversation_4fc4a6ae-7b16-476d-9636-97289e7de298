package com.qudian.lme.bifrost.api.vo.response;

import com.qudian.lme.bifrost.api.vo.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * {@inheritDoc} 主动查询转运轨迹响应
 *
 * <AUTHOR>
 * @since 2023/7/28
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class TransportTrackRespVO extends BaseResponseVO {

    /**
     * 返回的物流追踪信息
     */
    private List<TrackingResult> trackingResults;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Accessors(chain = true)
    public static class TrackingResult implements Serializable {

        private static final long serialVersionUID = 8946243967875777439L;
        /**
         * 运单单号
         */
        private String orderNumber;
        /**
         * 运单物流状态
         */
        private String status;
        /**
         * 追踪id
         */
        private String articleId;
        /**
         * 承运商
         */
        private String carrier;
        /**
         * 追踪包裹信息
         */
        private List<Event> events;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        @Accessors(chain = true)
        public static class Event implements Serializable {

            private static final long serialVersionUID = -3338646737464011273L;
            /**
             * 位置
             */
            private String location;
            /**
             * 事件说明
             */
            private String desc;
            /**
             * 时间，utc时区
             */
            private String date;
            /**
             * 毫秒时间戳
             */
            private Long eventTime;
            /**
             * 时区
             */
            private String eventTimeZone;
            /**
             * 邮编
             */
            private String eventZIPCode;
            /**
             * 城市
             */
            private String eventCity;
            /**
             * 州
             */
            private String eventState;
            /**
             * 国家
             */
            private String eventCountry;
            /**
             * 公司名称
             */
            private String firmName;

            private String gmtOffset;

            private String eventCode;
        }
    }
}

