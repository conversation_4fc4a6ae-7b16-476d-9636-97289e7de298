package com.qudian.lme.bifrost.api.vo.request;

import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class OfflineTrajectoryMappingReqDTO extends BaseRequestVO {

    /**
     * 单号
     */
    private List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfoList;


    //渠道
    private String channel;
    // 更新时间
    private Date updateTime;
    // 来源
    private String origin;
}
