package com.qudian.lme.bifrost.api.vo.request;

import com.qudian.lme.bifrost.api.enums.TransportStatusEnum;
import com.qudian.lme.bifrost.api.vo.PageRequestVO;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Title:  DeliverySettleListReqVO.java
 * @Package com.qudian.lme.bifrost.api.vo.request
 * @Description:  $DESC
 * @Author: zhouqingyang
 * @Date:   2023/12/19 19:55
 * @Version V1.0
 */
@Data
@NoArgsConstructor
public class DeliverySettleListReqVO extends PageRequestVO {

    private static final long serialVersionUID = 2829994792338800019L;

    /**
     * 状态枚举
     */
    private List<TransportStatusEnum> status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 主键id列表
     */
    private List<Long> ids;

    @Builder
    public DeliverySettleListReqVO(Long userId, int pageSize, int pageNum, Date startTime, Date endTime, List<TransportStatusEnum> status, List<Long> ids) {
        super(userId, pageSize, pageNum);
        this.startTime = startTime;
        this.endTime = endTime;
        this.status = status;
        this.ids = ids;
    }
}
