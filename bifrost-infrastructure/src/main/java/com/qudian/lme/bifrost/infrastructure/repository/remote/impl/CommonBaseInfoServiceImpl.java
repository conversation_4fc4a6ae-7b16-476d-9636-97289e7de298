package com.qudian.lme.bifrost.infrastructure.repository.remote.impl;

import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.lme.bifrost.common.constant.FileExtendConstant;
import com.qudian.lme.bifrost.common.utils.common.Utils;
import com.qudian.lme.bifrost.infrastructure.international.CountryFactory;
import com.qudian.lme.bifrost.infrastructure.repository.remote.CommonBaseInfoService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.OmsRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.AusTransPortAccountInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class CommonBaseInfoServiceImpl implements CommonBaseInfoService {


    @Value("${lms.wisway.state.mapping:[]}")
    private  String transPortAccountInfo;
    @Resource
    private OmsRemoteService omsRemoteService;
    @Resource
    private CountryFactory countryFactory;
    @Value("${performance.channels:[]}")
    private String performanceChannels;
    
    @Resource
    private ToolSrvRemote toolSrvRemote;

    @Override
    public List<String> splitAddressInfo(String address) {
        if (StringUtils.isBlank(address)){
            return Collections.emptyList();
        }
        return Utils.splitLength(address, 40);
    }

    @Override
    public String processCityCode(PushSingleTransportReqVO.RecipientInfo recipientInfo) {
        return StringUtils.isBlank(recipientInfo.getReceiptCity()) ? recipientInfo.getCounty() : recipientInfo.getReceiptCity();
    }

    @Override
    public String calculateTotalWeight(PushSingleTransportReqVO pushSingleTransportReqVO) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<PushSingleTransportReqVO.ItemInfo> itemInfo = pushSingleTransportReqVO.getShipment().getItemInfo();
        if (CollectionUtils.isNotEmpty(itemInfo)) {
            for (PushSingleTransportReqVO.ItemInfo item : itemInfo) {
                if (Objects.nonNull(item) && Objects.nonNull(item.getWeight())) {
                    totalAmount = totalAmount.add(BigDecimal.valueOf(item.getWeight()));
                }
            }
        }
        return totalAmount.toString();
    }

    @Override
    public AusTransPortAccountInfoDTO getAccountInfoByState(String state) {
        if (StringUtils.isBlank(state)){
            return null;
        }
        List<AusTransPortAccountInfoDTO> ausTransportApiConfigs = JSON.parseArray(transPortAccountInfo, AusTransPortAccountInfoDTO.class);
        if (ausTransportApiConfigs.isEmpty()) {
            return null;
        }
        return ausTransportApiConfigs.stream().filter(t->t.getState().equals(state)).findFirst().orElse(null);
    }

    @Override
    public String choiceChannel(String channel, String origin,String carrier) {
        List<String> channels = JSON.parseArray(performanceChannels, String.class);
        // 这里有两个来源，历史的转运 另外一种是履约系统转运 新增的是给履约系统使用的 履约的不需要改
        if (StringUtils.isBlank(origin)||channels.stream().noneMatch(t->t.equals(origin))){
             carrier = omsRemoteService.listCarrierByChannel(channel);
        }
        if (Objects.isNull(carrier)) carrier = countryFactory.get().getCarrier().getDefaultCarrier().getName();
        return carrier;
    }

    @Override
    public PrintLabelResponseVO parseBase64PdfAndUpload(String pdfString) {
        byte[] decodedBytes = Base64.getDecoder().decode(pdfString);
        LocalDateTime now = LocalDateTime.now();
        String format = DateTimeFormatter.ofPattern("yyyyMMddhhmmss").format(now);
        // 生成4位随机数
        int randomSuffix = RandomUtils.nextInt(1000, 9999);
        String fileName = format + randomSuffix + FileExtendConstant.PDF;
        // 上传pdf
        UploadFileRespVO uploadFileRespVO = toolSrvRemote.uploadFileV2(decodedBytes, fileName);
        return new PrintLabelResponseVO().setObjectKey(uploadFileRespVO.getData().getObject_key())
                .setSignedUrl(uploadFileRespVO.getData()
                .getSigned_url()).setExpiredAt(uploadFileRespVO.getData().getExpired_at());
    }

}
