package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.response;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2023/8/10 22:44
 */
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class TrackDetail {
    @XmlElement(name = "EventTime")
    private String eventTime;

    @XmlElement(name = "EventDate")
    private String eventDate;

    @XmlElement(name = "Event")
    private String event;

    @XmlElement(name = "GMT")
    private String GMT;

    @XmlElement(name = "GMTOffset")
    private String GMTOffset;

    @XmlElement(name = "EventCity")
    private String eventCity;

    @XmlElement(name = "EventState")
    private String eventState;

    @XmlElement(name = "EventZIPCode")
    private String eventZIPCode;

    @XmlElement(name = "FirmName")
    private String firmName;

    @XmlElement(name = "Name")
    private String name;
}
