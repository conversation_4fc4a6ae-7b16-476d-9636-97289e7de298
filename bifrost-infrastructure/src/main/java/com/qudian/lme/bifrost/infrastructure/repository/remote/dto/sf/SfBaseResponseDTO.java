package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/27
 */
// com.sf.express.model.response.SfBaseResponse.java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SfBaseResponseDTO {
    private String apiErrorMsg;
    private String apiResponseID;
    private String apiResultCode;

    private String apiResultData;


}
