package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "Envelope",namespace = "http://schemas.xmlsoap.org/soap/envelope/")
public class CreateOrderEnvelope {
    private Body body;

    @XmlElement(name = "Body",namespace = "http://schemas.xmlsoap.org/soap/envelope/")
    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }
    @XmlRootElement(name = "Body", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
    public static class Body{
        private CallServiceResponse callServiceResponse;

        @XmlElement(name = "callServiceResponse", namespace = "http://www.example.org/Ec/")
        public CallServiceResponse getCallServiceResponse() {
            return callServiceResponse;
        }

        public void setCallServiceResponse(CallServiceResponse callServiceResponse) {
            this.callServiceResponse = callServiceResponse;
        }
    }

    public static class CallServiceResponse{
        private String response;

        @XmlElement(name = "response")
        public String getResponse() {
            return response;
        }

        public void setResponse(String response) {
            this.response = response;
        }
    }
}
