package com.qudian.lme.bifrost.infrastructure.repository.remote;

import com.qudian.lme.bifrost.api.vo.request.ExpressDeliveryOrderInfo;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.track51.Track51AusPostResponseDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.track51.Track51TransportRequestVO;

import java.util.List;

/**
 * {@inheritDoc} 51track
 *
 * <AUTHOR>
 * @since 2023/10/16
 **/
public interface Track51RemoteService {


    List<Track51AusPostResponseDTO> getAusPostData(Track51TransportRequestVO reqVO, List<String> orderIds);


    /**
     * 订阅轨迹， 订阅了才可以查询 
     * @param expressDeliveryOrderInfoList
     */
    void subscribeTrace(List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfoList);

}
