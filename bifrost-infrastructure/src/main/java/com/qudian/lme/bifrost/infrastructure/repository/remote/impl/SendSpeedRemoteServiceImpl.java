package com.qudian.lme.bifrost.infrastructure.repository.remote.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.lme.bifrost.common.config.SendSpeedTransportApiConfig;
import com.qudian.lme.bifrost.common.constant.FileExtendConstant;
import com.qudian.lme.bifrost.common.enums.SpeedApiEnum;
import com.qudian.lme.bifrost.common.enums.SpeedReferenceNoEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.log.BizRequestLogParam;
import com.qudian.lme.bifrost.common.utils.HttpClient;
import com.qudian.lme.bifrost.common.utils.common.Utils;
import com.qudian.lme.bifrost.infrastructure.repository.remote.SendSpeedRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.BaseReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.BaseRespDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.CancelOrderDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.CancelOrderVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.CreateOrderDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.CreateOrderVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.PrintLabelDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.PrintLabelVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.SoapEnvelopeDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed.SoapMessageReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.impl.speed.SoapRequestGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class SendSpeedRemoteServiceImpl implements SendSpeedRemoteService {

    @Resource
    @Lazy
    private SoapRequestGenerator soapRequestGenerator;
    @Resource
    private HttpClient client;

    @Resource
    private SendSpeedTransportApiConfig sendSpeedTransportApiConfig;
    @Resource
    private ToolSrvRemote toolSrvRemote;
    private static final String FAILURE = "Failure";
    private static final Integer DEFAULT_PIECES = 1;
    private static final double DEFAULT_WEIGHT = 0.1;
    private static final float DEFAULT_LENGTH = 0.1f;

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {

        try {
            CancelOrderDTO cancelOrderDTO = CancelOrderDTO.builder()
                    .referenceNo(reqVO.getOrderNumber()).type(SpeedReferenceNoEnum.CUSTOMER_ORDER_NO.getCode()).build();
            SoapMessageReqDTO baseReqDTO = buildSoapMessageReqDTO(SpeedApiEnum.CANCEL_ORDER, JSON.toJSONString(cancelOrderDTO));
            BizRequestLogParam bizLogParam = buildBizLogParam(reqVO.getOrderNumber());
            String s = client.postXmlData(sendSpeedTransportApiConfig.getUrl(),
                    soapRequestGenerator.generateSoapRequest(baseReqDTO, SoapMessageReqDTO.class), bizLogParam);
            // 序列化对象
            SoapEnvelopeDTO o = (SoapEnvelopeDTO) soapRequestGenerator.parseSoapResponse(s, SoapEnvelopeDTO.class);
            CancelOrderVO cancelOrderVO = JSON.parseObject(o.getBody().getCallServiceResponse().getResponse(), CancelOrderVO.class);
            processErrorResp(cancelOrderVO);
        } catch (JAXBException e) {
            log.error("解析异常", e);
        }
    }

    @NotNull
    private SoapMessageReqDTO buildSoapMessageReqDTO(SpeedApiEnum speedApiEnum, String paramJson) {
        SoapMessageReqDTO baseReqDTO = new SoapMessageReqDTO();
        buildBaseReqDTO(baseReqDTO);
        baseReqDTO.setService(speedApiEnum.getRoute());
        baseReqDTO.setParamsJson(paramJson);
        return baseReqDTO;
    }

    @Override
    public PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO) {
        try {
            CreateOrderDTO createOrderDTO = buildCreateOrderBusinessInfo(reqVO);
            SoapMessageReqDTO baseReqDTO = buildSoapMessageReqDTO(SpeedApiEnum.CREATE_ORDER, JSON.toJSONString(createOrderDTO));
            BizRequestLogParam bizLogParam = buildBizLogParam(reqVO.getShipment().getOrderNumber());
            String s = client.postXmlData(sendSpeedTransportApiConfig.getUrl(),
                    soapRequestGenerator.generateSoapRequest(baseReqDTO, SoapMessageReqDTO.class), bizLogParam);
            SoapEnvelopeDTO o = (SoapEnvelopeDTO) soapRequestGenerator.parseSoapResponse(s, SoapEnvelopeDTO.class);
            CreateOrderVO cancelOrderVO = JSON.parseObject(o.getBody().getCallServiceResponse().getResponse(), CreateOrderVO.class);
            // 异常处理
            processErrorResp(cancelOrderVO);
            return PushSingleTransportResponseVO.builder().orderNumber(reqVO.getShipment().getOrderNumber())
                    .articleId(cancelOrderVO.getShippingMethodNo()).consignmentId(cancelOrderVO.getAgentNumber()).build();
        } catch (JAXBException e) {
            log.error("解析异常", e);
        }
        return null;
    }

    private static BizRequestLogParam buildBizLogParam(String reqVO) {
        return BizRequestLogParam.builder()
                .forwarderType(ForwarderTypeEnum.SPEED.getName())
                .carrier(CarrierTypeEnum.USPS.getName())
                .orderNumbers(Collections.singletonList(reqVO))
                .build();
    }

    private CreateOrderDTO buildCreateOrderBusinessInfo(PushSingleTransportReqVO reqVO) {
        PushSingleTransportReqVO.RecipientInfo recipientInfo = reqVO.getShipment().getRecipientInfo();
        PushSingleTransportReqVO.SenderInfo senderInfo = reqVO.getShipment().getSenderInfo();
        PushSingleTransportReqVO.ReturneeInfo returneeInfo = reqVO.getShipment().getReturneeInfo();
        CreateOrderDTO.CreateOrderDTOBuilder createOrderDTOBuilder = CreateOrderDTO.builder().referenceNo(reqVO.getShipment().getOrderNumber())
                .shippingMethod(sendSpeedTransportApiConfig.getShippingMethod())
                .countryCode("US").orderPieces(DEFAULT_PIECES);
        List<PushSingleTransportReqVO.ItemInfo> itemInfo = reqVO.getShipment().getItemInfo();
        setUpTotalWeight(createOrderDTOBuilder, itemInfo);
        setUpVolume(createOrderDTOBuilder, itemInfo);
        // 收件人信息
        CreateOrderDTO.Consignee consignee = setUpReceiverInfo(recipientInfo);
        // 寄件人信息
        CreateOrderDTO.Shipper shipper = setUpSenderInfo(senderInfo);
        // 退件信息
        CreateOrderDTO.ReturnTo returnInfo = setUpReturnInfo(returneeInfo);
        return createOrderDTOBuilder.shipper(shipper).consignee(consignee).returnTo(returnInfo).build();
    }

    private static CreateOrderDTO.ReturnTo setUpReturnInfo(PushSingleTransportReqVO.ReturneeInfo returneeInfo) {
        if (Objects.isNull(returneeInfo)) {
            return null;
        }
        CreateOrderDTO.ReturnTo returnTo = CreateOrderDTO.ReturnTo.builder().returnCountrycode(returneeInfo.getReturneeCountry())
                .returnPostcode(returneeInfo.getReturneePostcode())
                .returnName(returneeInfo.getReturnee())
                .returnCity(StringUtils.isBlank(returneeInfo.getReturneeCity()) ? returneeInfo.getCounty() : returneeInfo.getReturneeCity()).build();
        setUpReturnAddress(returneeInfo,returnTo);
        return returnTo;
    }

    private static void setUpTotalWeight(CreateOrderDTO.CreateOrderDTOBuilder createOrderDTOBuilder, List<PushSingleTransportReqVO.ItemInfo> itemInfo) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(itemInfo)) {
            for (PushSingleTransportReqVO.ItemInfo item : itemInfo) {
                if (Objects.nonNull(item) && Objects.nonNull(item.getWeight())) {
                    totalAmount = totalAmount.add(BigDecimal.valueOf(item.getWeight()));
                }
            }
        }
        createOrderDTOBuilder.orderWeight(BigDecimal.ZERO.equals(totalAmount) ? BigDecimal.valueOf(DEFAULT_WEIGHT) : totalAmount.setScale(2, RoundingMode.HALF_UP));
    }

    private static CreateOrderDTO.Shipper setUpSenderInfo(PushSingleTransportReqVO.SenderInfo senderInfo) {
        CreateOrderDTO.Shipper shipper = CreateOrderDTO.Shipper.builder().shipperProvince(senderInfo.getSenderProvince())
                .shipperCountrycode(senderInfo.getSenderCountry())
                .shipperCity(StringUtils.isBlank(senderInfo.getSenderCity()) ? senderInfo.getCounty() : senderInfo.getSenderCity())
                .shipperPostcode(senderInfo.getSenderPostcode()).shipperName(senderInfo.getSender()).build();
        setUpSenderAddress(senderInfo,shipper);
        return shipper;
    }

    private static CreateOrderDTO.Consignee setUpReceiverInfo(PushSingleTransportReqVO.RecipientInfo recipientInfo) {
        CreateOrderDTO.Consignee consignee = CreateOrderDTO.Consignee.builder().consigneeProvince(recipientInfo.getReceiptProvince())
                .consigneeCity(StringUtils.isBlank(recipientInfo.getReceiptCity()) ? recipientInfo.getCounty() : recipientInfo.getReceiptCity())
                .consigneeStreet(recipientInfo.getRecipientAddress())
                .consigneePostcode(recipientInfo.getReceiptPostcode()).consigneeName(recipientInfo.getRecipient()).build();
        setUpAddress(recipientInfo, consignee);
        return consignee;
    }

    private static void setUpAddress(PushSingleTransportReqVO.RecipientInfo recipientInfo, CreateOrderDTO.Consignee consignee) {
        List<String> addressList = Utils.splitLength(recipientInfo.getRecipientAddress(), 50);
        // 计算地址长度
        if (addressList.size()==1){
            consignee.setConsigneeStreet(addressList.get(0));
        }
        if (addressList.size()==2){
            consignee.setConsigneeStreet(addressList.get(0));
            consignee.setConsigneeStreet2(addressList.get(1));
        }else if (addressList.size()>=3){
            consignee.setConsigneeStreet(addressList.get(0));
            consignee.setConsigneeStreet2(addressList.get(1));
            consignee.setConsigneeStreet3(addressList.get(2));
        }else {
            consignee.setConsigneeStreet(recipientInfo.getRecipientAddress());
        }
    }

    private static void setUpSenderAddress(PushSingleTransportReqVO.SenderInfo senderInfo, CreateOrderDTO.Shipper shipper) {
        List<String> addressList = Utils.splitLength(senderInfo.getSenderAddress(), 50);
        // 计算地址长度
        if (addressList.size()==1){
            shipper.setShipperStreet(addressList.get(0));
        }
        if (addressList.size()==2){
            shipper.setShipperStreet(addressList.get(0));
            shipper.setShipperStreet2(addressList.get(1));
        }else if (addressList.size()>=3){
            shipper.setShipperStreet(addressList.get(0));
            shipper.setShipperStreet2(addressList.get(1));
            shipper.setShipperStreet3(addressList.get(2));
        }else {
            shipper.setShipperStreet(senderInfo.getSenderAddress());
        }
    }

    private static void setUpReturnAddress(PushSingleTransportReqVO.ReturneeInfo returneeInfo, CreateOrderDTO.ReturnTo returnTo) {
        List<String> addressList = Utils.splitLength(returneeInfo.getReturneeAddress(), 50);
        // 计算地址长度
        if (addressList.size()==1){
            returnTo.setReturnStreet(addressList.get(0));
        }
        if (addressList.size()==2){
            returnTo.setReturnStreet(addressList.get(0));
            returnTo.setReturnStreet2(addressList.get(1));
        }else if (addressList.size()>=3){
            returnTo.setReturnStreet(addressList.get(0));
            returnTo.setReturnStreet2(addressList.get(1));
            returnTo.setReturnStreet3(addressList.get(2));
        }else {
            returnTo.setReturnStreet(addressList.get(0));
        }
    }


    private static void setUpVolume(CreateOrderDTO.CreateOrderDTOBuilder createOrderDTOBuilder, List<PushSingleTransportReqVO.ItemInfo> itemInfo) {
        if (CollectionUtils.isNotEmpty(itemInfo) && Objects.nonNull(itemInfo.get(0))) {
            PushSingleTransportReqVO.ItemInfo item = itemInfo.get(0);
            createOrderDTOBuilder.volume(CreateOrderDTO.Volume.builder()
                    .width(Objects.isNull(item.getWidth()) ? DEFAULT_LENGTH : item.getWidth().floatValue())
                    .length(Objects.isNull(item.getLength()) ? DEFAULT_LENGTH : item.getLength().floatValue())
                    .height(Objects.isNull(item.getHeight()) ? DEFAULT_LENGTH : item.getHeight().floatValue()).build());
        } else {

            createOrderDTOBuilder.volume(CreateOrderDTO.Volume.builder()
                    .width(DEFAULT_LENGTH).length(DEFAULT_LENGTH).height(DEFAULT_LENGTH).build());
        }
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        try {
            String orderNumber = reqVO.getOrderNumber();

            PrintLabelDTO labelDTO = PrintLabelDTO.builder().referenceNo(orderNumber)
                    .type(SpeedReferenceNoEnum.CUSTOMER_ORDER_NO.getCode()).build();
            BizRequestLogParam bizLogParam = buildBizLogParam(reqVO.getOrderNumber());
            SoapMessageReqDTO baseReqDTO = buildSoapMessageReqDTO(SpeedApiEnum.PRINT_LABEL, JSON.toJSONString(labelDTO));
            String s = client.postXmlData(sendSpeedTransportApiConfig.getUrl(), soapRequestGenerator.generateSoapRequest(baseReqDTO, SoapMessageReqDTO.class), bizLogParam);
            SoapEnvelopeDTO o = (SoapEnvelopeDTO) soapRequestGenerator.parseSoapResponse(s, SoapEnvelopeDTO.class);
            PrintLabelVO printLabelVO = JSON.parseObject(o.getBody().getCallServiceResponse().getResponse(), PrintLabelVO.class);
            // 异常处理
            processErrorResp(printLabelVO);
            byte[] data = client.getByteData(printLabelVO.getUrl());
            UploadFileRespVO uploadFileRespVO = toolSrvRemote.uploadFileV2(data, DateUtil.format(new Date(), "yyyyMMhhHHmmssSSS") + FileExtendConstant.PDF);
            return PrintLabelResponseVO.builder().objectKey(uploadFileRespVO.getData().getObject_key()).signedUrl(uploadFileRespVO.getData()
                    .getSigned_url()).expiredAt(uploadFileRespVO.getData().getExpired_at()).pdfUrl(printLabelVO.getUrl()).build();
        } catch (JAXBException e) {
            log.error("解析异常", e);
        }
        return null;

    }

    public void buildBaseReqDTO(BaseReqDTO baseReqDTO) {
        baseReqDTO.setAppKey(sendSpeedTransportApiConfig.getAppKey());
        baseReqDTO.setAppToken(sendSpeedTransportApiConfig.getAppToken());
    }

    public void processErrorResp(BaseRespDTO baseRespDTO) {
        if (FAILURE.equals(baseRespDTO.getAsk())) {
            if (Objects.nonNull(baseRespDTO.getError())) {
                throw new BizException(baseRespDTO.getError().getErrMessage());
            }
            throw new BizException(baseRespDTO.getMessage());
        }
    }
}
