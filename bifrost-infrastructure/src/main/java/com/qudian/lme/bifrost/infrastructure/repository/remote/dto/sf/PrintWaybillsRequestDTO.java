package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrintWaybillsRequestDTO {

    private String templateCode;
    private String version;
    private String fileType;
    private boolean sync;
    private List<Document> documents;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Accessors(chain = true)
    public static class Document{
        private String masterWaybillNo;

    }

}
