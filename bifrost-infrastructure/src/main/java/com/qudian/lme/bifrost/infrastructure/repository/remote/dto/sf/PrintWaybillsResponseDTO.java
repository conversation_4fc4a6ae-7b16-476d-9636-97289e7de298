package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrintWaybillsResponseDTO extends RequestIdDTO {
    private boolean success;
    private PrintWaybillsResponse obj;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class PrintWaybillsResponse {
        private String clientCode;
        private String fileType;
        private List<File> files;
        private String templateCode;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @Accessors(chain = true)
        public static class File {

            private int areaNo;
            private int documentSize;
            private int pageCount;
            private int pageNo;
            private int seqNo;
            private String token;
            private String url;
            private String waybillNo;

        }
    }

}
