package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.response;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Getter
@XmlRootElement(name = "TrackResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class TrackResponseDTO {

    @XmlElement(name = "TrackInfo")
    private List<TrackInfoDTO> trackInfoDTOList;
}
