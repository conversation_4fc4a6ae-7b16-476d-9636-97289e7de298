package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Huang
 * @date 2025/8/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SfBaseRequestDTO {
    @JSONField(name = "partnerID") // 对应顺丰接口的partnerID（即appKey）
    private String partnerId;

    @JSONField(name = "requestID") // 唯一请求ID（官网要求32位以内）
    private String requestId;
    private String serviceCode;
    private String msgDigest;
    private String accessToken;

    private Long timestamp; // 改用Date类型，由FastJSON自动格式化
}