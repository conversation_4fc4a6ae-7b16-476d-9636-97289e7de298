package com.qudian.lme.bifrost.infrastructure.repository.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModifyShipmentDTO {
    private String username;
    private String digest;
    private String msgType;
    private String version;
    private String referenceNo;
    private From from;
    private To to;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class From {
        private String name;
        private String phone;
        private String suburb;
        private String state;
        private List<String> lines;
        private String postcode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class To {
        private String name;
        private String phone;
        private String suburb;
        private String state;
        private List<String> lines;
        private String postcode;
    }
}
