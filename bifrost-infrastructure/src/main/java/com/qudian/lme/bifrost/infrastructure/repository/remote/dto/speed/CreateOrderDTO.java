package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class CreateOrderDTO implements Serializable {
    @JSONField(name = "reference_no")
    private String referenceNo;

    @JSONField(name = "shipping_method")
    private String shippingMethod;

    @JSONField(name = "country_code")
    private String countryCode;

    @JSONField(name = "order_weight")
    private BigDecimal orderWeight;

    @JSONField(name = "order_pieces")
    private Integer orderPieces;

    @JSONField(name = "mail_cargo_type")
    private String mailCargoType;

    @JSONField(name = "cargo_type")
    private String cargoType;

    @JSONField(name = "sales_amount")
    private Float salesAmount;

    @JSONField(name = "sales_currency")
    private String salesCurrency;

    @JSONField(name = "total_amount_currency")
    private BigDecimal totalAmountCurrency;

    @J<PERSON>NField(name = "ioss")
    private String ioss;

    @JSONField(name = "Consignee")
    private Consignee consignee;

    @JSONField(name = "Shipper")
    private Shipper shipper;

    @JSONField(name = "ReturnTo")
    private ReturnTo returnTo;

    @JSONField(name = "ItemArr")
    private List<ItemArr> itemArr;
    @JSONField(name = "Volume")
    private Volume volume;

    @JSONField(name = "extraService")
    private String extraService;

    @Data
    @Builder
    public static class Consignee implements Serializable {
        @JSONField(name = "consignee_company")
        private String consigneeCompany;
        @JSONField(name = "consignee_province")
        private String consigneeProvince;
        @JSONField(name = "consignee_city")
        private String consigneeCity;
        @JSONField(name = "consignee_street")
        private String consigneeStreet;
        @JSONField(name = "consignee_street2")
        private String consigneeStreet2;
        @JSONField(name = "consignee_street3")
        private String consigneeStreet3;
        @JSONField(name = "consignee_district")
        private String consigneeDistrict;
        @JSONField(name = "consignee_postcode")
        private String consigneePostcode;
        @JSONField(name = "consignee_name")
        private String consigneeName;
        @JSONField(name = "consignee_telephone")
        private String consigneeTelephone;
        @JSONField(name = "consignee_mobile")
        private String consigneeMobile;
        @JSONField(name = "consignee_email")
        private String consigneeEmail;
        @JSONField(name = "consignee_doorplate")
        private String consigneeDoorplate;
        @JSONField(name = "consignee_taxno")
        private String consigneeTaxno;
        @JSONField(name = "consignee_taxno_type")
        private String consigneeTaxnoType;
        @JSONField(name = "consignee_eori")
        private String consigneeEori;
    }
    @Data
    @Builder
    public static class Shipper implements Serializable{
        @JSONField(name = "shipper_company")
        private String shipperCompany;
        @JSONField(name = "shipper_countrycode")
        private String shipperCountrycode;
        @JSONField(name = "shipper_province")
        private String shipperProvince;
        @JSONField(name = "shipper_city")
        private String shipperCity;
        @JSONField(name = "shipper_street")
        private String shipperStreet;
        @JSONField(name = "shipper_street2")
        private String shipperStreet2;
        @JSONField(name = "shipper_street3")
        private String shipperStreet3;
        @JSONField(name = "shipper_postcode")
        private String shipperPostcode;
        @JSONField(name = "shipper_name")
        private String shipperName;
        @JSONField(name = "shipper_telephone")
        private String shipperTelephone;
        @JSONField(name = "shipper_mobile")
        private String shipperMobile;
        @JSONField(name = "shipper_email")
        private String shipperEmail;
        @JSONField(name = "shipper_doorplate")
        private String shipperDoorplate;
        @JSONField(name = "shipper_tax_number")
        private String shipperTaxNumber;
        @JSONField(name = "shipper_tax_number_type")
        private String shipperTaxNumberType;
        @JSONField(name = "shipper_eori")
        private String shipperEori;
    }

    @Data
    @Builder
    public static class ReturnTo implements Serializable{
        @JSONField(name = "return_company")
        private String returnCompany;
        @JSONField(name = "return_countrycode")
        private String returnCountrycode;
        @JSONField(name = "return_province")
        private String returnProvince;
        @JSONField(name = "return_city")
        private String returnCity;
        @JSONField(name = "return_street")
        private String returnStreet;
        @JSONField(name = "return_street2")
        private String returnStreet2;
        @JSONField(name = "return_street3")
        private String returnStreet3;
        @JSONField(name = "return_postcode")
        private String returnPostcode;
        @JSONField(name = "return_name")
        private String returnName;
        @JSONField(name = "return_telephone")
        private String returnTelephone;
        @JSONField(name = "return_mobile")
        private String returnMobile;
        @JSONField(name = "return_email")
        private String returnEmail;
        @JSONField(name = "return_doorplate")
        private String returnDoorplate;
        @JSONField(name = "return_tax_number")
        private String returnTaxNumber;
        @JSONField(name = "return_tax_number_type")
        private String returnTaxNumberType;
        @JSONField(name = "return_eori")
        private String returnEori;
    }

    @Data
    @Builder
    public static class ItemArr implements Serializable{
        @JSONField(name = "invoice_enname")
        private String invoiceEnname;
        @JSONField(name = "invoice_cnname")
        private String invoiceCnname;
        @JSONField(name = "invoice_weight")
        private String invoiceWeight;
        @JSONField(name = "invoice_quantity")
        private Integer invoiceQuantity;
        @JSONField(name = "unit_code")
        private String unitCode;
        @JSONField(name = "invoice_unitcharge")
        private String invoiceUnitcharge;
        @JSONField(name = "invoice_currencycode")
        private String invoiceCurrencycode;
        @JSONField(name = "hs_code")
        private String hsCode;
        private String sku;
        @JSONField(name = "box_number")
        private String boxNumber;
        private String material;
        @JSONField(name = "material_enture")
        private String materialEnture;
        private String use;
        @JSONField(name = "invoice_brand")
        private String invoiceBrand;
        @JSONField(name = "invoice_brand_type")
        private String invoiceBrandType;
        @JSONField(name = "is_magnetoelectric")
        private String isMagnetoelectric;
        @JSONField(name = "deal_price")
        private Float dealPrice;
        @JSONField(name = "country_of_origin")
        private String countryOfOrigin;
    }

    @Data
    @Builder
    public static class Volume implements Serializable{
        private Float length;
        private Float width;
        private Float height;
        @JSONField(name = "child_number")
        private String childNumber;

        @JSONField(name = "box_number")
        private String boxNumber;

        @JSONField(name = "refer_number")
        private String referNumber;

    }
}
