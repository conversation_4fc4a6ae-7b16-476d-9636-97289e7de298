package com.qudian.lme.bifrost.infrastructure.repository.remote;

import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;

public interface SendSpeedRemoteService {
    void cancelTransport(CancelTransportReqVO reqVO);

    PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO);

    PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO);
}
