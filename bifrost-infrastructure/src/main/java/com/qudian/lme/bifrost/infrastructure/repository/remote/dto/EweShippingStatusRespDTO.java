package com.qudian.lme.bifrost.infrastructure.repository.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * {@inheritDoc} 主动查询物流状态响应DTO
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EweShippingStatusRespDTO {

    private List<TrackingResult> tracking_results;

    @Data
    public static class TrackingResult implements Serializable {

        private String tracking_id;
        private String sender_reference;
        private String status;
        private List<TrackableItem> trackable_items;
        private List<Error> errors;
        @Data
        public static class TrackableItem {

            private String article_id;
            private String product_type;
            private String status;
            private List<Event> events;

            @Data
            public static class Event {

                private String location;
                private String description;
                private String date;
            }


        }
        @Data
        public static class Error {
            private String code;
            private String name;
            private String message;
        }
    }
}
