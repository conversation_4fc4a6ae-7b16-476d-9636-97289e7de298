package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.exp;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: yang<PERSON>ye
 * @Date: 2024/4/9
 * @Version: 1.0.0
 **/
@Data
@Accessors(chain = true)
@XmlRootElement(namespace = "http://www.asmred.com/", name = "GetExpCli")
public class GetExpCli {

    private String uid;
    private String codigo;

}
