package com.qudian.lme.bifrost.infrastructure.repository.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.lme.bifrost.api.vo.request.DeliverySettleListReqVO;
import com.qudian.lme.bifrost.api.vo.response.DeliverySettleListResVO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单转运状态流程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Mapper
public interface OrderTransportMapper extends BaseMapper<OrderTransportPO> {

    @Update("<script>" + "UPDATE bifrost_order_transport " +
            "<set>" +
            "<if test ='finishTime!=null'>finish_time =#{finishTime},</if>"+
            "status = #{targetStatus}, updated_time = now() " +
            "</set>" +
            "WHERE delete_flag = 0 AND order_number IN " +
            "        <foreach collection='orderNumbers' item='orderNumber' separator=',' open='(' close=')'>\n" +
            "             #{orderNumber}" +
            "        </foreach>" + "</script>")
    void updateStatusAndFinishTime(@Param("targetStatus") Integer targetStatus, @Param("orderNumbers") List<String> orderNumbers ,@Param("finishTime") Date finishTime);

    @Update("<script>" + "UPDATE bifrost_order_transport " +
            "<set>" +
            "<if test ='objectKey!=null'>object_key =#{objectKey},</if>"+
            "<if test ='signedUrl!=null'>signed_url = #{signedUrl},</if>"+
            "<if test ='pdfUrl!=null'>pdf_url = #{pdfUrl},</if>"+
            "updated_time = now()"+
            "</set>" +
            "WHERE delete_flag = 0  AND order_number IN "+
            "        <foreach collection='orderNumbers' item='orderNumber' separator=',' open='(' close=')'>\n" +
            "             #{orderNumber}" +
            "        </foreach>" + "</script>")
    void updateObjectKey(@Param("objectKey") String objectKey,
                         @Param("signedUrl") String signedUrl, @Param("orderNumbers") List<String> orderNumbers,
                         @Param("pdfUrl")String pdfUrl);

    @Update("<script>" + "UPDATE bifrost_order_transport SET status = #{targetStatus}, updated_time = now() WHERE delete_flag = 0 AND status = #{sourceStatus} AND order_number IN " +
            "        <foreach collection='orderNumbers' item='orderNumber' separator=',' open='(' close=')'>\n" +
            "             #{orderNumber}" +
            "        </foreach>" + "</script>")
    void updateStatusOrigin(@Param("sourceStatus") Integer sourceStatus, @Param("targetStatus") Integer targetStatus, @Param("orderNumbers") List<String> orderNumbers);

    @Select("<script>" + "SELECT * FROM bifrost_order_transport WHERE delete_flag = 0 AND order_number IN " +
     "        <foreach collection='orderNumbers' item='orderNumber' separator=',' open='(' close=')'>\n" +
             "             #{orderNumber}" +
             "        </foreach>" + "</script>")
    List<OrderTransportPO> queryOrderTransportList(@Param("orderNumbers") List<String> orderNumbers);

    OrderTransportPO queryOrderTransportOne(@Param("orderNumber") String orderNumber, @Param("deleteFlag") Integer deleteFlag);

    @Update("<script>" + "UPDATE bifrost_order_transport SET delete_flag = 1, status = #{targetStatus}, updated_time = now() WHERE delete_flag = 0 AND order_number IN " +
            "        <foreach collection='orderNumbers' item='orderNumber' separator=',' open='(' close=')'>\n" +
            "             #{orderNumber}" +
            "        </foreach>" + "</script>")
    Integer updateStatusAndDeleteFlag(@Param("targetStatus") Integer targetStatus, @Param("orderNumbers") List<String> orderNumbers);

    @Select( "<script>" +"SELECT id FROM bifrost_order_transport WHERE status IN " +
        "        <foreach collection='statusList' item='item' separator=',' open='(' close=')'>\n" +
        "             #{item}" +
        "        </foreach>"+
        "AND carrier = #{carrier}"+ "</script>")
    List<Long> selectIdByStatusListAndCarrier(@Param("statusList") List<Integer> statusList, @Param("carrier") String carrier);

    @Select("<script>" + "SELECT * FROM bifrost_order_transport WHERE id IN" +
        "        <foreach collection='ids' item='item' separator=',' open='(' close=')'>\n" +
        "            #{item}" +
        "        </foreach>" + "AND delete_flag = 0" + "</script>")
    List<OrderTransportPO> selectAllByIds(@Param("ids") List<Long> ids);

    @Update("UPDATE bifrost_order_transport SET status = #{status} WHERE id = #{id}")
    void updateStatusById(@Param("id") Long id, @Param("status") Integer status);

    /**
     * finish_time勿随意传参，只能在finish_time未设置时间才能设置,业务代码层判断
     */
    @Update("<script>" + "UPDATE bifrost_order_transport " +
            "<set>" +
            "<if test ='finishTime!=null'>finish_time =#{finishTime},</if>"+
            "status = #{status} " +
            "</set>" +
            " WHERE id = #{id}" + "</script>")
    void updateStatusAndFinishTimeById(@Param("id") Long id, @Param("status") Integer status ,@Param("finishTime") Date finishTime);

    @Update("UPDATE bifrost_order_transport SET forwarder_status = #{forwarderStatus} WHERE id = #{id}")
    void updateForwarderStatus(@Param("id") Long id, @Param("forwarderStatus") String forwarderStatus) ;

    @Select({"<script>",
            "SELECT * from bifrost_order_transport where delete_flag=0 ",
            "<if test='reqVO.startTime != null' > and finish_time &gt;= #{reqVO.startTime} </if>",
            "<if test='reqVO.endTime != null' > and finish_time &lt;= #{reqVO.endTime} </if>",
            "<if test='reqVO.status != null and reqVO.status.size > 0' > and status in ",
                "<foreach collection='reqVO.status' item='item' open='(' separator=',' close=')'> #{item.code}",
                "</foreach>",
            " </if>",
            "<if test='!@org.springframework.util.CollectionUtils@isEmpty(reqVO.ids)' > and id IN ",
                "<foreach collection='reqVO.ids' item='id' open='(' separator=',' close=')'> #{id}",
                "</foreach>",
            " </if>",
            "order by id desc",
            "</script>"})
    List<DeliverySettleListResVO> querySettleList(@Param("reqVO") DeliverySettleListReqVO reqVO);
}
