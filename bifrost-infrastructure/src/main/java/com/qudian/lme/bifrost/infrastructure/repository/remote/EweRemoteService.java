package com.qudian.lme.bifrost.infrastructure.repository.remote;

import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.ModifyTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.OrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.TransportTrackReqVO;
import com.qudian.lme.bifrost.api.vo.response.PushTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.EweShippingStatusRespDTO;

import java.util.List;

public interface EweRemoteService {

    List<PushTransportResponseVO> pushShipment(PushTransportReqVO reqVO);

    Boolean dispatchTransport(OrderTransportReqVO reqVO);

    Boolean cancelTransport(CancelTransportReqVO reqVO);

    Boolean modifyTransportAddress(ModifyTransportReqVO reqVO);

    Boolean printLabel(OrderTransportReqVO reqVO);

    EweShippingStatusRespDTO shippingStatus(TransportTrackReqVO reqVO);

    UploadFileRespVO printLabelV2(OrderTransportReqVO reqVO);
}
