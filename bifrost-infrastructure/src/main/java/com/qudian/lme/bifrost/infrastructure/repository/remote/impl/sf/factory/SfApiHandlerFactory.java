package com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.factory;

import com.google.common.collect.Maps;
import com.qudian.lme.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.AbstractSfApiTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
@Slf4j
@Component
public class SfApiHandlerFactory {
    @Resource
    private List<AbstractSfApiTemplate> sfApiHandlers;

    private final Map<SfApiCodeEnum, AbstractSfApiTemplate> SF_API_HANDLER_MAP = Maps.newConcurrentMap();

    @PostConstruct
    private void init() {
        SF_API_HANDLER_MAP.putAll(sfApiHandlers.stream().collect(Collectors.toMap(
                AbstractSfApiTemplate::support,
                        Function.identity(),
                        (o1, o2) -> o1
                )
        ));
    }

    public AbstractSfApiTemplate getHandler(SfApiCodeEnum apiCodeEnum) {
        Objects.requireNonNull(apiCodeEnum, "apiCodeEnum is null");
        AbstractSfApiTemplate apiTemplate = SF_API_HANDLER_MAP.get(apiCodeEnum);
        if (Objects.isNull(apiTemplate)) {
            throw new BizException("sf api handler not found, code: " + apiCodeEnum.getCode());
        }
        return apiTemplate;
    }
}
