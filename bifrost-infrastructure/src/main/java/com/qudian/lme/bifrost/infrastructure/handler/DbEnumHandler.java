package com.qudian.lme.bifrost.infrastructure.handler;

import com.qudian.lme.bifrost.api.enums.IBaseEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;


/**
 * 封装通用的mybatis枚举映射处理器
 *
 * <AUTHOR>
 */
public class DbEnumHandler extends BaseTypeHandler<IBaseEnum> {

    private Map<Object, IBaseEnum> enumMap;

    public DbEnumHandler(Class<IBaseEnum> type) {
        enumMap = new HashMap<>();
        IBaseEnum[] constants = type.getEnumConstants();
        if (constants == null) {
            return;
        }

        for (IBaseEnum baseEnum : constants) {
            enumMap.put(baseEnum.getCode(), baseEnum);
        }
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, IBaseEnum baseEnum, JdbcType jdbcType) throws SQLException {
        preparedStatement.setObject(i, baseEnum.getCode());
    }

    @Override
    public IBaseEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return enumMap.get(resultSet.getObject(s));
    }

    @Override
    public IBaseEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return enumMap.get(resultSet.getObject(i));
    }

    @Override
    public IBaseEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        Object val = callableStatement.getObject(i);
        return enumMap.get(val);
    }
}