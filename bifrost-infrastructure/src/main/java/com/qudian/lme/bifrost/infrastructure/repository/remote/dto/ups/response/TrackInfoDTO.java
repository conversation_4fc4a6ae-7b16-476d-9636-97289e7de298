package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.response;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class TrackInfoDTO {
    @XmlAttribute(name = "ID")
    private String id;

    @XmlElement(name = "DestinationCity")
    private String destinationCity;

    @XmlElement(name = "DestinationState")
    private String destinationState;

    @XmlElement(name = "DestinationZip")
    private String destinationZip;

    @XmlElement(name = "OriginCity")
    private String originCity;

    @XmlElement(name = "OriginState")
    private String originState;

    @XmlElement(name = "OriginZip")
    private String originZip;

    @XmlElement(name = "Service")
    private String service;

    @XmlElement(name = "ServiceTypeCode")
    private String serviceTypeCode;

    @XmlElement(name = "Status")
    private String status;

    @XmlElement(name = "StatusCategory")
    private String statusCategory;

    @XmlElement(name = "StatusSummary")
    private String statusSummary;

    @XmlElement(name = "TrackSummary")
    private TrackDetail trackSummary;

    @XmlElement(name = "TrackDetail")
    private List<TrackDetail> trackDetailList;

    @XmlElement(name = "Error")
    private Error error;
}
