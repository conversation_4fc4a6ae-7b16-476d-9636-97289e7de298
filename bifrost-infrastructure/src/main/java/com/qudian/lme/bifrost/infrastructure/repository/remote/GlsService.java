package com.qudian.lme.bifrost.infrastructure.repository.remote;

import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;

public interface GlsService {
    void cancelTransport(CancelTransportReqVO reqVO);

    PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO);

    PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO);

    TransportTrackRespVO shippingStatus(ShippingStatusReqDTO reqVO);
}
