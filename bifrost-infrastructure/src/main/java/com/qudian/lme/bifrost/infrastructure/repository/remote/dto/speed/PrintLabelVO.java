package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrintLabelVO extends BaseRespDTO implements Serializable {
    @JSONField(name = "reference_no")
    private String referenceNo;
    private String url;
}
