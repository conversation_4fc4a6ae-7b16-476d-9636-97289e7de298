package com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.strategy;

import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import java.net.URLEncoder;
import java.security.MessageDigest;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
// com.sf.express.sign.SfSignGenerator.java
@Component
public class SfSignGenerator implements SignStrategy{

    public  String generateSign(String msgData,String timestamp,String checkWord) {
        try {

            //将业务报文+时间戳+校验码组合成需加密的字符串(注意顺序)
            String toVerifyText = msgData + timestamp + checkWord;

            //因业务报文中可能包含加号、空格等特殊字符，需要urlEnCode处理
            toVerifyText = URLEncoder.encode(toVerifyText, "UTF-8");

            //进行Md5加密
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(toVerifyText.getBytes("UTF-8"));
            byte[] md = md5.digest();

            //通过BASE64生成数字签名
            String msgDigest = new String(new BASE64Encoder().encode(md));
            return msgDigest;
        } catch (Exception e) {
            throw new RuntimeException("生成顺丰签名失败", e);
        }
    }

//    public static void main(String[] args) {
//        //客户校验码    使用顺丰分配的客户校验码
//        String checkWord = "YlG8QAHKVCvCxF8tujqn4g4Ala1jE20V";
//
//        //时间戳 取报文中的timestamp（调用接口时间戳）
//        String timestamp = "123213213213";
//
//        //业务报文  去报文中的msgData（业务数据报文）
//        String msgData = "{\"cargoDesc\":\"苹果\",\"cargoDetails\":[{\"amount\":100.5111,\"count\":2.365,\"currency\":\"HKD\",\"goodPrepardNo\":\"AAAA002\",\"hsCode\":\"AAAA004\",\"name\":\"护肤品1\",\"productRecordNo\":\"AAAA001\",\"sourceArea\":\"CHN\",\"taxNo\":\"AAAA003\",\"unit\":\"个\",\"weight\":6.1}],\"contactInfoList\":[{\"address\":\"软件产业基地11栋\",\"city\":\"深圳市\",\"contact\":\"顺小丰\",\"contactType\":1,\"country\":\"CN\",\"county\":\"南山区\",\"mobile\":\"13480155048\",\"postCode\":\"580058\",\"province\":\"广东省\",\"tel\":\"4006789888\"},{\"address\":\"广东省广州市白云区湖北大厦\",\"city\":\"\",\"contact\":\"顺小丰\",\"contactType\":2,\"country\":\"CN\",\"county\":\"\",\"mobile\":\"13925211148\",\"postCode\":\"580058\",\"province\":\"\",\"tel\":\"18688806057\"}],\"language\":\"zh-CN\",\"orderId\":\"F2_20200604180946\"}";
//
//        System.out.println(generateSign(msgData,timestamp,checkWord));
//    }
}