package com.qudian.lme.bifrost.infrastructure.international.country;

import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.infrastructure.international.CountryBase;
import com.qudian.lme.bifrost.infrastructure.international.CountryDataVO;
import com.qudian.lme.bifrost.common.enums.CountryCodeEnum;
import org.springframework.stereotype.Component;

/**
 * <p>文件名称:com.qudian.lme.driver.business.factory.pushAppMsg.cell.PickUpPushAppMsgCell</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/2/16
 */
@Component
public class Australia extends CountryBase {

    @Override
    public CountryCodeEnum support() {
        return CountryCodeEnum.AUSTRALIA;
    }

    @Override
    public CountryDataVO.Carrier getCarrier() {
        return CountryDataVO.Carrier.ForwarderBuilder()
            .supportCarriers(Lists.newArrayList(CarrierTypeEnum.AUS_POST,CarrierTypeEnum.AUS_POST_WISE_WAY))
            .defaultCarrier(CarrierTypeEnum.AUS_POST)
            .build();
    }

}
