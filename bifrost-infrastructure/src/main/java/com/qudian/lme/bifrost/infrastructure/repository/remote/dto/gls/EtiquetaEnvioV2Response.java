package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "EtiquetaEnvioResponse", namespace = "http://www.asmred.com/")
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
public class EtiquetaEnvioV2Response {
    @XmlElement(name = "EtiquetaEnvioResult",namespace = "http://www.asmred.com/")
    private EtiquetaEnvioV2Result EtiquetaEnvioV2Result;
    @Getter
    public static class EtiquetaEnvioV2Result {
        @XmlElement(name = "base64Binary",namespace = "http://www.asmred.com/")
        private String base64Binary;
    }
}
