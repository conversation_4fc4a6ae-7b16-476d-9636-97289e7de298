package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ErrorDetailVO {
    @JSONField(name = "error_code")
    private Integer errorCode;

    @JSONField(name = "error_datetime")
    private String errorDatetime;

    @JSONField(name = "error_type")
    private String errorType;

    @JSONField(name = "error_description")
    private String errorDescription;
}
