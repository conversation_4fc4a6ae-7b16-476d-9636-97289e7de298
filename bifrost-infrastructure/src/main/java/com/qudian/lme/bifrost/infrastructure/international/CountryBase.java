package com.qudian.lme.bifrost.infrastructure.international;

import com.qudian.lme.bifrost.common.enums.CountryCodeEnum;
import org.springframework.context.support.ApplicationObjectSupport;

/**
 * <p>文件名称:com.qudian.lme.driver.business.factory.pushAppMsg.cell.PushAppMsgBase</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/2/16
 */
public class CountryBase extends ApplicationObjectSupport implements CountryInterface {


    @Override
    public CountryDataVO info(CountryCodeEnum codeEnum) {
        return CountryDataVO.builder()
                .carrier(getCarrier())
                .build();
    }

    @Override
    public CountryCodeEnum support() {
        throw new UnsupportedOperationException();
    }

    public CountryDataVO.Carrier getCarrier() {
        throw new UnsupportedOperationException();
    }

}
