package com.qudian.lme.bifrost.infrastructure.repository.remote;

import com.qudian.lme.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.lme.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderRequestDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.CreateOrderResponseDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsRequestDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsResponseDTO;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
public interface ExpressRemote {
    CreateOrderResponseDTO createOrder(CreateOrderRequestDTO createOrderRequestDTO);


    PrintWaybillsResponseDTO printWaybills(PrintWaybillsRequestDTO printWaybillsRequestDTO);




}
