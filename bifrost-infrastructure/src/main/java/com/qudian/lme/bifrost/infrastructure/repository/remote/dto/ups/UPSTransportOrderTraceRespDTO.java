package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UPSTransportOrderTraceRespDTO {

    private String trackId;
    private String status;
    private String statusCategory;
    private String statusSummary;

    private TrackDetail trackSummary;

    private List<TrackDetail> trackDetailList;

    @Data
    public static class TrackDetail {
        private Date eventTime;
        private String event;
        private String gmt;
        private String gmtOffset;
        private String eventCity;
        private String eventState;
        private String eventZIPCode;
        private String firmName;
        private String name;
    }
}
