package com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf;

import cn.hutool.core.util.StrUtil;
import com.qudian.lme.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.lme.bifrost.api.vo.BaseRequestVO;
import com.qudian.lme.bifrost.api.vo.BaseResponseVO;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.utils.HttpClient;
import com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.strategy.SignStrategy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */
@Slf4j
@Component
public abstract class AbstractSfApiTemplate<V extends BaseRequestVO, T, R extends BaseResponseVO> {
    @Resource
    private HttpClient httpClient;
    @Value("${express.sf.partnerID:YJP2E5E5}")
    private String partnerID;
    @Value("${express.sf.secret:YlG8QAHKVCvCxF8tujqn4g4Ala1jE20V}")
    private String secret;
    @Value("${express.sf.baseUrl:https://sfapi-sbox.sf-express.com/std/service}")
    private String baseUrl;
    @Resource
    protected SignStrategy signStrategy;


    // 模板方法：定义通用流程（final修饰，禁止子类修改）
    public final R execute(V reqVO) {
        T requestDTO = convert(reqVO);
        String requestID = generateRequestID();
        if(StrUtil.isBlank(reqVO.getRequestId())){
            reqVO.setRequestId(requestID);
        }
        String timestamp = generateTimestamp();
        try {
            // 1. 构建业务参数JSON（抽象方法，子类实现）
            String msgData = buildMsgData(requestDTO);
            log.info("[op={}]请求参数, requestID={}, msgData={}", getServiceCode(), requestID, msgData);

            // 2. 生成签名（通用逻辑）
            String msgDigest = signStrategy.generateSign(msgData, timestamp, secret);

            // 3. 构建请求体（通用逻辑）
            RequestBody formBody = buildFormBody(requestID, msgData, timestamp, msgDigest);

            // 4. 构建HTTP请求（通用逻辑）
            Request request = buildRequest(formBody);

            // 5. 发送请求并处理响应（通用逻辑）
            return sendRequestAndHandleResponse(reqVO, request);

        } catch (BizException e) {
            log.error("[op={}]业务异常, requestID={}", getServiceCode(), requestID, e);
            throw e;
        } catch (Exception e) {
            log.error("[op={}]系统异常, requestID={}", getServiceCode(), requestID, e);
            throw new BizException(getServiceCode() + "接口调用失败, requestID=" + requestID);
        }
    }

    // 生成唯一请求ID（通用）
    protected String generateRequestID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    // 生成时间戳（通用，可覆写）
    protected String generateTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    // 构建表单请求体（通用）
    protected RequestBody buildFormBody(String requestID, String msgData, String timestamp, String msgDigest) {
        return new FormBody.Builder()
                .add("partnerID", partnerID)
                .add("requestID", "QDIDLE_" + requestID)
                .add("serviceCode", getServiceCode()) // 服务代码由子类提供
                .add("timestamp", timestamp)
                .add("msgDigest", msgDigest)
                .add("msgData", msgData)
                .build();
    }

    // 构建HTTP请求（通用）
    protected Request buildRequest(RequestBody formBody) {
        return new Request.Builder()
                .url(baseUrl)
                .post(formBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
    }

    // 发送请求并处理响应（通用）
    protected R sendRequestAndHandleResponse(V reqVO, Request request) throws IOException {
        try (Response response = httpClient.getOkHttpClient().newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            log.info("[op={}]响应, requestID={}, code={}, responseBody={}",
                    getServiceCode(), reqVO.getRequestId(), response.code(), responseBody);

            if (response.isSuccessful()) {
                // 解析响应（抽象方法，子类实现）
                R parseResponse = parseResponse(reqVO, responseBody);
                parseResponse.setRequestId(reqVO.getRequestId());
                return parseResponse;
            } else {
                throw new BizException(getServiceCode() + "接口调用失败, requestID=" + reqVO.getRequestId()
                        + ", code=" + response.code() + ", message=" + responseBody);
            }
        }
    }

    // 抽象方法：获取服务代码（如EXP_RECE_CREATE_ORDER）
    protected abstract String getServiceCode();

    //抽象方法： 支持的 api
    public abstract SfApiCodeEnum support();

    // 抽象方法：构建业务参数JSON
    protected abstract String buildMsgData(T requestDTO);

    // 抽象方法：解析响应为业务对象
    protected abstract R parseResponse(V reqVO, String responseBody);

    // 抽象方法：转换VO为DTO（子类实现）
    protected abstract T convert(V reqVO);
}