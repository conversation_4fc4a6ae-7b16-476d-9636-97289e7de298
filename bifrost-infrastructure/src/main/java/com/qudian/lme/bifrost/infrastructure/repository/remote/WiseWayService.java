package com.qudian.lme.bifrost.infrastructure.repository.remote;

import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackReqVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackRespVO;

import java.util.List;

public interface WiseWayService {
    PushSingleTransportResponseVO pushShipment(PushSingleTransportReqVO reqVO);

    PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO);

     void cancelTransport(CancelTransportReqVO reqVO);


     void  dispatchSingleTransport(SingleOrderTransportReqVO singleOrderTransportReqVO);



     List<WiseWayOrderTrackRespVO> queryOrderTrack(WiseWayOrderTrackReqVO reqVO);
}
