package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TokenDTO {
    
    @JSONField(name = "client_id")
    private String clientId;

    @JSONField(name = "client_secret")
    private String clientSecret;

    @JSONField(name = "scope")
    private String scope;

    @JSONField(name = "grant_type")
    private String grantType;
    
}
