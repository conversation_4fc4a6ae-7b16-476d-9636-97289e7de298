package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls;


import lombok.Data;

import javax.xml.bind.annotation.*;
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class Destinatario {
    @XmlElement(name = "Codigo")
    private String codigo;

    @XmlElement(name = "Plaza")
    private String plaza;

    @XmlElement(name = "Nombre")
    private String nombre;

    @XmlElement(name = "Direccion")
    private String direccion;

    @XmlElement(name = "Poblacion")
    private String poblacion;

    @XmlElement(name = "Provincia")
    private String provincia;

    @XmlElement(name = "Pais")
    private String pais;

    @XmlElement(name = "CP")
    private String cp;

    @XmlElement(name = "Telefono")
    private String telefono;

    @XmlElement(name = "Movil")
    private String movil;

    @XmlElement(name = "Email")
    private String email;

    @XmlElement(name = "Observaciones")
    private String observaciones;

    @XmlElement(name = "ATT")
    private String att;

    // Getters and setters
    // toString method for printing the object
}
