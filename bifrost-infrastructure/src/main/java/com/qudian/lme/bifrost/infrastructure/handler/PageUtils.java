package com.qudian.lme.bifrost.infrastructure.handler;

import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.qudian.lme.bifrost.api.vo.PageRequestVO;
import com.qudian.lme.bifrost.api.vo.PagingList;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Title:  PageUtils.java
 * @Package com.qudian.lme.bifrost.infrastructure.utils
 * @Description:  $DESC
 * @Author: zhouqingyang
 * @Date:   2023/12/20 17:45
 * @Version V1.0
 */
public class PageUtils {

    public static Pageable pageJPA(PageRequestVO page) {
        Pageable pageable = PageRequest.of(page.getPageNum() - 1, page.getPageSize());
        return pageable;
    }

    public static <E> Page<E> pageMybatis(PageRequestVO page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize());
    }

    public static <T> PageInfo<T> getPageInfo(PageRequestVO page, ISelect select) {
        return pageMybatis(page).doSelectPageInfo(select);
    }

    public static <T> PagingList getPagingList(PageRequestVO page, ISelect select) {
        return getPagingList(getPageInfo(page, select));
    }

    public static <T> PagingList getPagingList(PageInfo<T> page) {
        PagingList pagingList = new PagingList();
        pagingList.setTotal(page.getTotal());
        pagingList.setPageSize(page.getPageSize());
        pagingList.setPageNum(page.getPageNum());
        pagingList.setHasMore(page.isHasNextPage());
        pagingList.setResultList(page.getList());
        return pagingList;
    }

    public static <T> PagingList getPagingList(Page page, List<T> result) {
        PagingList pagingList = new PagingList();
        pagingList.setTotal(page.getTotal());
        pagingList.setPageSize(page.getPageSize());
        pagingList.setPageNum(page.getPageNum());
        pagingList.setResultList(result);
        pagingList.setHasMore(page.getPageNum() * page.getPageSize() < page.getTotal());
        return pagingList;
    }

    public static <T> PagingList getPagingList(org.springframework.data.domain.Page page, List<T> result) {
        PagingList pagingList = new PagingList();
        pagingList.setTotal(page.getTotalElements());
        pagingList.setPageSize(page.getSize());
        pagingList.setPageNum(page.getNumber() + 1);
        pagingList.setResultList(result);
        pagingList.setHasMore(page.hasNext());
        return pagingList;
    }

    public static <T> PagingList getPagingList(Page<T> page) {
        return getPagingList(page, page.getResult());
    }

}
