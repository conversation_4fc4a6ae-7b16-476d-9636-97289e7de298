package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CancelOrderDTO {
    /**
     * 订单号
     */
    @JSONField(name="reference_no")
    private String referenceNo;

    /**
     * 1-运单号,2-客户订单号,3-跟踪号
     */
    private String type;
}
