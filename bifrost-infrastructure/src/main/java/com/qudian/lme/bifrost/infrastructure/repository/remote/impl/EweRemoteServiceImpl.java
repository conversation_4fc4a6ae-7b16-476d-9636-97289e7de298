package com.qudian.lme.bifrost.infrastructure.repository.remote.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.ModifyTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.OrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.TransportTrackReqVO;
import com.qudian.lme.bifrost.api.vo.response.ErrorResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.lme.bifrost.common.config.AusTransportApiConfig;
import com.qudian.lme.bifrost.common.constant.AusTransportConstant;
import com.qudian.lme.bifrost.common.constant.FileExtendConstant;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.log.BizRequestLogParam;
import com.qudian.lme.bifrost.common.utils.GeneratorDigestUtil;
import com.qudian.lme.bifrost.common.utils.HttpClient;
import com.qudian.lme.bifrost.common.utils.common.Utils;
import com.qudian.lme.bifrost.infrastructure.repository.remote.EweRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.AusTransPortAccountInfoDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.CancelShipmentDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.DispatchShipmentDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.EweShippingStatusReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.EweShippingStatusRespDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ModifyShipmentDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.PushShipmentDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.SpecialAusTransPortAccountInfoDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class EweRemoteServiceImpl implements EweRemoteService {

    private static final String AREA_CODE = "+61";

    private static final String PARAPHRASE_AREA_CODE = "\\+61";

    @Resource
    private AusTransportApiConfig ausTransportApiConfig;

    @Resource
    private ToolSrvRemote toolSrvRemote;

    @Resource
    private HttpClient client;

    @Value("${aus.transport.account.info:[]}")
    private  String transPortAccountInfo;


    @Value("${aus.transport.special.account.info:[]}")
    private String specialChannelAccountInfo;
    @Value("${performance.channels:[]}")
    private String performanceChannels;


    @Override
    public List<PushTransportResponseVO> pushShipment(PushTransportReqVO reqVO) {
        List<PushShipmentDTO.Shipment> shipments = new ArrayList<>();
        List<String> orderNumbers = new ArrayList<>();
        reqVO.getShipments().forEach(vo -> {
            orderNumbers.add(vo.getOrderNumber());
            // 封装发件人信息
            PushTransportReqVO.SenderInformation senderInformation = vo.getSenderInformation();
            PushShipmentDTO.From from = PushShipmentDTO.From.builder()
                .name(senderInformation.getSender())
                .country(senderInformation.getSenderCountry())
                .phone(senderInformation.getSenderMobilePhone())
                // 优先使用county
                .suburb(isEmptyOrDefault(senderInformation.getCounty(), senderInformation.getSenderCity()))
                .state(senderInformation.getSenderProvince())
                .lines(Utils.splitLength(senderInformation.getSenderAddress(), 40))
                .postcode(senderInformation.getSenderPostcode()).build();

            // 封装收件人信息
            PushTransportReqVO.RecipientInformation recipientInformation = vo.getRecipientInformation();
            PushShipmentDTO.To to = PushShipmentDTO.To.builder()
                .name(recipientInformation.getTo())
                .country(recipientInformation.getReceiptCountry())
                .phone(dealPhoneIllegalCharacter(recipientInformation.getRecipientMobilePhone()))
                .suburb(isEmptyOrDefault(recipientInformation.getReceiptCity(), recipientInformation.getCounty()))
                .state(recipientInformation.getReceiptProvince())
                .lines(Utils.splitLength(recipientInformation.getRecipientAddress(), 40))
                .postcode(recipientInformation.getReceiptPostcode()).build();

            // 封装货物信息
            List<PushTransportReqVO.ItemInformation> itemInformation = vo.getItemInformation();
            List<PushShipmentDTO.Item> items = new ArrayList<>();
            itemInformation.forEach(it -> {
                PushShipmentDTO.Item item = PushShipmentDTO.Item.builder()
                        .item_description(it.getItemDescription())
                        .product_id(AusTransportConstant.PARCEL_POST)
                        .width(AusTransportConstant.DEFAULT_WIDTH)
                        .height(AusTransportConstant.DEFAULT_HEIGHT)
                        .length(AusTransportConstant.DEFAULT_LENGTH)
                        .weight(AusTransportConstant.DEFAULT_WEIGHT).build();
                items.add(item);
            });

            PushShipmentDTO.Shipment shipment = PushShipmentDTO.Shipment.builder()
                .to(to)
                .from(from)
                .items(items)
                .sender_reference(vo.getOrderNumber()).build();
            shipments.add(shipment);
        });
        PushShipmentDTO.PushShipmentDTOBuilder builder = PushShipmentDTO.builder();
        AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = getAccountInfoByStateAndOrigin(reqVO.getWarehouseLocationState(),reqVO.getOrigin());
        if (Objects.isNull(ausTransPortAccountInfoDTO)){
            builder.username(ausTransportApiConfig.getUsername());
            builder.digest(GeneratorDigestUtil.generatorPushUnCreatedShipmentsDigest(ausTransportApiConfig.getUsername(), ausTransportApiConfig.getPassword(), reqVO.getShipments().size()));
        }else {
            // 如果channel是特殊的channel 需要使用特殊的账号，原因：主体不一样，所用的账号不一样
            builder.username(ausTransPortAccountInfoDTO.getUserName());
            builder.digest(GeneratorDigestUtil.generatorPushUnCreatedShipmentsDigest(ausTransPortAccountInfoDTO.getUserName(),
                    ausTransPortAccountInfoDTO.getPassword(),
                    reqVO.getShipments().size()));
        }
        PushShipmentDTO pushShipmentDTO = builder
            .shipments(shipments)
            .version(AusTransportConstant.VERSION)
            .msgType(AusTransportConstant.PUSH_UNCREATED_SHIPMENTS)
             .build();

        BizRequestLogParam param = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .orderNumbers(orderNumbers)
            .build();

        String responseBody = client.postJavascriptDataNoLimit(ausTransportApiConfig.getHost() + ausTransportApiConfig.getPushUnCreatedShipments(), JSON.toJSONString(pushShipmentDTO), param);
        if (responseBody == null) {
            throw new BizException("push shipments fail");
        }
        JSONObject responseJsonBody = JSON.parseObject(responseBody);
        Integer code = responseJsonBody.getInteger("code");
        if (Objects.equals(code, 0)) {
            return JSON.parseArray(responseJsonBody.getString("shipments"), PushTransportResponseVO.class);
        }
        if (Objects.equals(code, -1)) {
            return JSON.parseArray(responseJsonBody.getString("shipments"), PushTransportResponseVO.class);
        }
        throw new BizException(parseError(responseJsonBody));
    }

    public String dealPhoneIllegalCharacter(String phone) {
        // 需要处理的电话号码
        // +61485885313,7437#
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        if (phone.startsWith(AREA_CODE) && !Objects.equals(phone.indexOf(","), -1)) {
            // 处理后的
            // 0485885313
            return phone.split(",")[0].replaceAll(PARAPHRASE_AREA_CODE, "0");
        }
        return phone;
    }

    public Boolean isNullOrZero(Double target) {
        return Objects.isNull(target) || BigDecimal.ZERO.compareTo(BigDecimal.valueOf(target)) == 0;
    }

    public String isEmptyOrDefault(String target, String defaultValue) {
        return StringUtils.isEmpty(target) ? defaultValue : target;
    }

    private String parseError(JSONObject responseJsonBody) {
        Object errors = responseJsonBody.get("errors");
        if (errors instanceof JSONObject) {
            return JSON.parseObject(String.valueOf(errors), ErrorResponseVO.class).getMessage();
        }
        List<String> errorList = new ArrayList<>();
        if (errors instanceof JSONArray) {
            JSONArray errorArray = responseJsonBody.getJSONArray("errors");
            for (Object o : errorArray) {
                ErrorResponseVO errorResponseVO = JSON.parseObject(String.valueOf(o), ErrorResponseVO.class);
                StringBuilder stringBuffer = new StringBuilder();
                if (StringUtils.isNotBlank(errorResponseVO.getSender_reference())){
                    stringBuffer.append(errorResponseVO.getSender_reference()).append(":");
                }
                if (StringUtils.isNotBlank(errorResponseVO.getMessage())){
                    stringBuffer.append(errorResponseVO.getMessage());
                }
                errorList.add(stringBuffer.toString());
            }
        }
        return StringUtils.join(errorList, ",");
    }

    @Override
    public Boolean dispatchTransport(OrderTransportReqVO reqVO) {
        DispatchShipmentDTO.DispatchShipmentDTOBuilder builder = DispatchShipmentDTO.builder()
            .msgType(AusTransportConstant.DISPATCH_SHIPMENTS)
            .sender_references(reqVO.getOrderNumbers());
        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .orderNumbers(reqVO.getOrderNumbers())
            .build();

        AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = getAccountInfoByStateAndOrigin(reqVO.getState(),reqVO.getOrigin());
        if (Objects.isNull(ausTransPortAccountInfoDTO)){
            builder.username(ausTransportApiConfig.getUsername());
            builder.digest(GeneratorDigestUtil.generatorDispatchShipmentsDigest(ausTransportApiConfig.getUsername(), ausTransportApiConfig.getPassword(), reqVO.getOrderNumbers().size()));
        }else {
            builder.username(ausTransPortAccountInfoDTO.getUserName());
            builder.digest(GeneratorDigestUtil.generatorDispatchShipmentsDigest(ausTransPortAccountInfoDTO.getUserName(),
                ausTransPortAccountInfoDTO.getPassword(),
                reqVO.getOrderNumbers().size()));
        }
        DispatchShipmentDTO dispatchShipmentDTO = builder.build();
        String responseBody = client.postJavascriptDataNoLimit(ausTransportApiConfig.getHost() + ausTransportApiConfig.getDispatchShipments(), JSON.toJSONString(dispatchShipmentDTO), bizLogParam);
        if (responseBody == null) {
            throw new BizException("dispatch transport fail");
        }
        JSONObject responseJsonBody = JSON.parseObject(responseBody);
        Boolean isSuccess = responseJsonBody.getBoolean("success");
        if (!isSuccess) {
            throw new BizException(responseJsonBody.getString("message"));
        }
        return isSuccess;
    }

    @Override
    public Boolean cancelTransport(CancelTransportReqVO reqVO) {

        CancelShipmentDTO.CancelShipmentDTOBuilder builder = CancelShipmentDTO.builder();
        AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = getAccountInfoByStateAndOrigin(reqVO.getState(),reqVO.getOrigin());
        if (Objects.isNull(ausTransPortAccountInfoDTO)){
            builder.username(ausTransportApiConfig.getUsername());
            builder.digest(GeneratorDigestUtil.generatorCancelShipmentDigest(ausTransportApiConfig.getUsername(), ausTransportApiConfig.getPassword()));
        }else {
            builder.username(ausTransPortAccountInfoDTO.getUserName());
            builder.digest(GeneratorDigestUtil.generatorCancelShipmentDigest(ausTransPortAccountInfoDTO.getUserName(), ausTransPortAccountInfoDTO.getPassword()));
        }
        CancelShipmentDTO dispatchShipmentDTO = builder
            .msgType(AusTransportConstant.CANCEL_SHIPMENT)
            .referenceNo(reqVO.getOrderNumber())
            .version(AusTransportConstant.VERSION_16)
            .reason(reqVO.getReason()).build();
        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .orderNumbers(Lists.newArrayList(reqVO.getOrderNumber()))
            .build();
        String responseBody = client.postJavascriptDataNoLimit(ausTransportApiConfig.getHost() + ausTransportApiConfig.getCancelShipment(), JSON.toJSONString(dispatchShipmentDTO), bizLogParam);
        if (responseBody == null) {
            throw new BizException("cancel transport fail");
        }
        JSONObject responseJsonBody = JSON.parseObject(responseBody);
        Boolean isSuccess = responseJsonBody.getBoolean("success");
        if (!isSuccess) {
            throw new BizException(responseJsonBody.getString("message"));
        }
        return isSuccess;
    }

    @Deprecated
    @Override
    public Boolean modifyTransportAddress(ModifyTransportReqVO reqVO) {
        // 发件人信息
        ModifyTransportReqVO.SenderInformation senderInformation = reqVO.getSenderInformation();
        ModifyShipmentDTO.From from = ModifyShipmentDTO.From.builder()
            .name(senderInformation.getSender())
            .suburb(senderInformation.getSenderCity())
            .state(senderInformation.getSenderProvince())
            .phone(senderInformation.getSenderMobilePhone())
            .lines(Utils.splitLength(senderInformation.getSenderAddress(), 40))
            .postcode(senderInformation.getSenderPostcode()).build();

        // 收件人信息
        ModifyTransportReqVO.RecipientInformation recipientInformation = reqVO.getRecipientInformation();
        ModifyShipmentDTO.To to = ModifyShipmentDTO.To.builder()
            .name(recipientInformation.getTo())
            .suburb(recipientInformation.getReceiptCity())
            .state(recipientInformation.getReceiptProvince())
            .phone(dealPhoneIllegalCharacter(recipientInformation.getRecipientMobilePhone()))
            .lines(Utils.splitLength(recipientInformation.getRecipientAddress(), 40))
            .postcode(recipientInformation.getReceiptPostcode()).build();

        ModifyShipmentDTO modifyShipmentDTO = ModifyShipmentDTO.builder()
            .referenceNo(reqVO.getOrderNumber())
            .from(from)
            .to(to)
            .username(ausTransportApiConfig.getUsername())
            .version(AusTransportConstant.VERSION_16)
            .msgType(AusTransportConstant.MODIFY_SHIPMENT)
            .digest(GeneratorDigestUtil.generatorModifyShipmentDigest(ausTransportApiConfig.getUsername(), ausTransportApiConfig.getPassword())).build();
        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .orderNumbers(Lists.newArrayList(reqVO.getOrderNumber()))
            .build();

        String responseBody = client.postJavascriptDataNoLimit(ausTransportApiConfig.getHost() + ausTransportApiConfig.getModifyShipment(), JSON.toJSONString(modifyShipmentDTO), bizLogParam);
        if (responseBody == null) {
            throw new BizException("modify address fail");
        }
        JSONObject responseJsonBody = JSON.parseObject(responseBody);
        Boolean isSuccess = responseJsonBody.getBoolean("success");
        if (!isSuccess) {
            throw new BizException(responseJsonBody.getString("message"));
        }
        return isSuccess;
    }

    @Deprecated
    @Override
    public Boolean printLabel(OrderTransportReqVO reqVO) {
        DispatchShipmentDTO dispatchShipmentDTO = DispatchShipmentDTO.builder()
            .sender_references(reqVO.getOrderNumbers())
            .username(ausTransportApiConfig.getUsername()).build();
        try {
            log.info("printLabel request  : {}", JSON.toJSONString(dispatchShipmentDTO));
            // todo 后来人解耦掉
            BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
                .forwarderType(ForwarderTypeEnum.EWE.getName())
                .carrier(CarrierTypeEnum.AUS_POST.getName())
                .orderNumbers(reqVO.getOrderNumbers())
                .build();

            Response response = client.postJavascriptDataReturnResponse(ausTransportApiConfig.getHost() + ausTransportApiConfig.getPrintLabel(), JSON.toJSONString(dispatchShipmentDTO), bizLogParam);
            if (!response.isSuccessful()) {
                throw new IOException("printLabel failure " + response);
            }
            String header = response.header("Content-Type");
            if (Objects.equals("application/pdf", header)) {
                byte[] data = response.body().bytes();
                ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletResponse httpServletResponse = servletRequestAttributes.getResponse();
                httpServletResponse.setContentType("application/pdf");
                OutputStream stream = httpServletResponse.getOutputStream();
                stream.write(data);
                stream.flush();
                stream.close();
                return Boolean.TRUE;
            } else {
                String responseBody = response.body().string();
                log.info("printLabel response : {}", responseBody);
                JSONObject responseJsonBody = JSON.parseObject(responseBody);
                throw new BizException(responseJsonBody.getString("message"));
            }
        } catch (IOException e) {
            log.error("printLabel request exception: {}", e);
            throw new BizException("printLabel failure");
        }
    }

    @Override
    public EweShippingStatusRespDTO shippingStatus(TransportTrackReqVO reqVO) {
        EweShippingStatusReqDTO.EweShippingStatusReqDTOBuilder builder = EweShippingStatusReqDTO.builder()
            .timezone(ausTransportApiConfig.getTimezone())
            .sender_reference(reqVO.getOrderNumberList());
        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .orderNumbers(reqVO.getOrderNumberList())
            .build();

        AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = getAccountInfoByStateAndOrigin(reqVO.getState(), reqVO.getOrigin().orElse(null));
        if (Objects.isNull(ausTransPortAccountInfoDTO)){
            builder.username(ausTransportApiConfig.getUsername());
        }else {
            builder.username(ausTransPortAccountInfoDTO.getUserName());
        }
        String responseBody = client.postJavascriptDataNoLimit(ausTransportApiConfig.getHost() + ausTransportApiConfig.getShippingStatus(), JSON.toJSONString(builder.build()), bizLogParam);
        if (responseBody == null) {
            throw new BizException("shipping status Failure");
        }
        JSONObject responseJsonBody = JSON.parseObject(responseBody);
        EweShippingStatusRespDTO respDTO = responseJsonBody.toJavaObject(EweShippingStatusRespDTO.class);
        if (respDTO.getTracking_results().isEmpty()) {
            log.error("query shipping status fail, responseBody : {}", responseBody);
            throw new BizException("query shipping status failure");
        }
        return respDTO;
    }

    @Override
    public UploadFileRespVO printLabelV2(OrderTransportReqVO reqVO) {
        DispatchShipmentDTO.DispatchShipmentDTOBuilder builder = DispatchShipmentDTO.builder();

        String state = reqVO.getState();
        AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = getAccountInfoByStateAndOrigin(state,reqVO.getOrigin());
        if (Objects.isNull(ausTransPortAccountInfoDTO)){
            builder.username(ausTransportApiConfig.getUsername());
        }else {
            builder.username(ausTransPortAccountInfoDTO.getUserName());
        }
        DispatchShipmentDTO dispatchShipmentDTO = builder
            .sender_references(reqVO.getOrderNumbers()).build();

        BizRequestLogParam bizLogParam = BizRequestLogParam.builder()
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .orderNumbers(reqVO.getOrderNumbers())
            .build();
        try {
            log.info("printLabelV2 request param : {}", JSON.toJSONString(dispatchShipmentDTO));
            Response response = client.postJavascriptDataReturnResponse(ausTransportApiConfig.getHost() + ausTransportApiConfig.getPrintLabel(), JSON.toJSONString(dispatchShipmentDTO), bizLogParam);
            if (!response.isSuccessful()) {
                throw new IOException("printLabelV2 failure " + response);
            }
            String header = response.header("Content-Type");
            if (Objects.equals("application/pdf", header)) {
                byte[] data = response.body().bytes();
                return toolSrvRemote.uploadFileV2(data, DateUtil.format(new Date(), "yyyyMMhhHHmmssSSS") + FileExtendConstant.PDF);
            } else {
                String responseBody = response.body().string();
                log.info("printLabelV2 response : {}", responseBody);
                JSONObject responseJsonBody = JSON.parseObject(responseBody);
                throw new BizException(responseJsonBody.getString("message"));
            }
        } catch (IOException e) {
            log.error("printLabelV2 request exception: {}", e);
            throw new BizException("printLabel failure");
        }
    }

    public AusTransPortAccountInfoDTO getAccountInfoByStateAndOrigin(String state, String origin) {
        if (StringUtils.isBlank(state)){
            return null;
        }
        else {
            return getAusTransPortAccountInfoDTOByConfiguration(state, origin);
        }
    }

    @Nullable
    private AusTransPortAccountInfoDTO getAusTransPortAccountInfoDTOByConfiguration(String state, String origin) {
        if (StringUtils.isNotBlank(origin)&&JSON.parseArray(performanceChannels,String.class).stream().anyMatch(origin::equals)){
            AusTransPortAccountInfoDTO ausTransPortAccountInfoDTO = checkConfigurationSpecialAccountInfo(state, origin);
            if (Objects.nonNull(ausTransPortAccountInfoDTO)){
                return ausTransPortAccountInfoDTO;
            }
            log.error("电商来源的配置了帐号密码但是没有找到请排查,来源:{},state:{}",origin,state);
        }
        // 直接根据state去找
        List<AusTransPortAccountInfoDTO> ausTransportApiConfigs = JSON.parseArray(transPortAccountInfo, AusTransPortAccountInfoDTO.class);
        if (ausTransportApiConfigs.isEmpty()) {
            return null;
        }
        return ausTransportApiConfigs.stream().filter(t -> t.getState().equals(state)).findFirst().orElse(null);
    }

    private AusTransPortAccountInfoDTO checkConfigurationSpecialAccountInfo(String state, String origin) {
        // 根据channel和state去找对应的账号
        List<SpecialAusTransPortAccountInfoDTO> specialAusTransPortAccountInfoDTOS = JSON.parseArray(specialChannelAccountInfo, SpecialAusTransPortAccountInfoDTO.class);
        if (CollectionUtils.isNotEmpty(specialAusTransPortAccountInfoDTOS)&& specialAusTransPortAccountInfoDTOS.stream().anyMatch(t->t.getOrigin().equals(origin))){
            SpecialAusTransPortAccountInfoDTO specialAusTransPortAccountInfoDTO = specialAusTransPortAccountInfoDTOS.stream().filter(t -> t.getOrigin().equals(origin)).findFirst().orElse(null);
            if (Objects.nonNull(specialAusTransPortAccountInfoDTO)){
                return specialAusTransPortAccountInfoDTO.getAccountList().stream().filter(t -> t.getState().equals(state)).findFirst().orElse(null);
            }
        }
        return null;
    }
}
