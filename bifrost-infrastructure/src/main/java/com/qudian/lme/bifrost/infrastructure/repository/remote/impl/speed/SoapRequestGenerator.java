package com.qudian.lme.bifrost.infrastructure.repository.remote.impl.speed;

import com.qudian.lme.bifrost.common.exception.BizException;
import com.sun.xml.bind.marshaller.NamespacePrefixMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.w3c.dom.CDATASection;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.PropertyException;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class SoapRequestGenerator {

    public String generateSoapRequest(Object data, Class<?> dataClass) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(dataClass);

        // 创建 marshaller
        Marshaller marshaller = context.createMarshaller();

        // 设置命名空间前缀映射
        marshaller.setProperty("com.sun.xml.bind.namespacePrefixMapper", new CustomNamespacePrefixMapper());

        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

        // 移除XML声明
        marshaller.setProperty("com.sun.xml.bind.xmlDeclaration", Boolean.FALSE);
        StringWriter writer = new StringWriter();
        writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"); // 添加XML声明
        writer.write("<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ns1=\"http://www.example.org/Ec/\">\n");
        writer.write("    <SOAP-ENV:Body>\n");
        marshaller.marshal(data, writer);
        writer.write("    </SOAP-ENV:Body>\n");
        writer.write("</SOAP-ENV:Envelope>");

        return writer.toString();
    }

    public String generateV2GLsSoapRequest(Object data, Class<?> dataClass,String name) {
        try {
            if (StringUtils.isBlank(name)){
                throw new BizException("请输入正确的方法名字");
            }
            // 创建SOAP消息工厂
            MessageFactory factory = MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL);
            SOAPMessage message = factory.createMessage();

            // 获取SOAP消息的部分
            SOAPPart soapPart = message.getSOAPPart();

            // 创建SOAP消息的消息体
            SOAPEnvelope envelope = soapPart.getEnvelope();
            envelope.setPrefix("soap12");
            envelope.removeNamespaceDeclaration("env");
            envelope.addNamespaceDeclaration("xsd", "http://www.w3.org/2001/XMLSchema");
            envelope.addNamespaceDeclaration("xsi", "http://www.w3.org/2001/XMLSchema-instance");
            SOAPBody body = envelope.getBody();
            body.setPrefix("soap12");
            String string = getString(data,dataClass);
            CDATASection cdata = soapPart.createCDATASection(string);
            SOAPElement soapElement = body.addChildElement(name, "", "http://www.asmred.com/");
            soapElement.appendChild(cdata);
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
            transformer.setOutputProperty(OutputKeys.METHOD, "xml");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(message.getSOAPPart()), new StreamResult(writer));
            String xmlString = writer.toString();
            byte[] stringBytes = xmlString.getBytes(StandardCharsets.UTF_8);
            return new String(stringBytes, StandardCharsets.UTF_8);
        }catch (Exception e){
           log.error("构建soap失败",e);
           throw new BizException("构建soap消息失败");
        }
    }


    public String generateV2GLsSoapRequest(Object data, Class<?> dataClass) {
        try {
            // 创建SOAP消息工厂
            MessageFactory factory = MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL);
            SOAPMessage message = factory.createMessage();

            // 获取SOAP消息的部分
            SOAPPart soapPart = message.getSOAPPart();

            // 创建SOAP消息的消息体
            SOAPEnvelope envelope = soapPart.getEnvelope();
            envelope.setPrefix("soap12");
            envelope.removeNamespaceDeclaration("env");
            envelope.addNamespaceDeclaration("xsd", "http://www.w3.org/2001/XMLSchema");
            envelope.addNamespaceDeclaration("xsi", "http://www.w3.org/2001/XMLSchema-instance");
            SOAPBody body = envelope.getBody();
            body.setPrefix("soap12");
            String string = getString(data,dataClass);
            try {
                // 创建DOM解析器工厂
                DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
                dbf.setNamespaceAware(true); // 设置命名空间感知

                // 使用DOM解析器工厂创建DOM解析器
                org.w3c.dom.Document document = dbf.newDocumentBuilder().parse(new org.xml.sax.InputSource(new java.io.StringReader(string)));

                // 将DOM元素导入到SOAP消息的Body中
                 body.addDocument(document);
            } catch (Exception e) {
                e.printStackTrace();
            }
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
            transformer.setOutputProperty(OutputKeys.METHOD, "xml");
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(message.getSOAPPart()), new StreamResult(writer));
            String xmlString = writer.toString();
            byte[] stringBytes = xmlString.getBytes(StandardCharsets.UTF_8);
            return new String(stringBytes, StandardCharsets.UTF_8);
        }catch (Exception e){
            log.error("构建soap失败",e);
            throw new BizException("构建soap消息失败");
        }
    }
    public Object parseSoapResponse(String soapResponse,Class<?> dataClass)  {
       try {
           JAXBContext context = JAXBContext.newInstance(dataClass);
           Unmarshaller unmarshaller = context.createUnmarshaller();
           return unmarshaller.unmarshal(new StringReader(soapResponse));
       }catch (Exception e){
           log.error("parseSoapResponse error:",e);
           throw new BizException("解析bean失败");
       }
    }



    private static String getString(Object object,Class<?> dataClass) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(dataClass);

        // 创建 marshaller
        Marshaller marshaller = context.createMarshaller();

        // 禁用命名空间
        try {
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
        } catch (PropertyException e) {
            e.printStackTrace();
        }

        // 设置格式化输出和编码
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
        marshaller.setProperty("com.sun.xml.bind.namespacePrefixMapper", new NamespacePrefixMapper() {
            @Override
            public String getPreferredPrefix(String namespaceUri, String suggestion, boolean requirePrefix) {
                // 如果是默认命名空间，则返回空字符串，否则返回建议的前缀
                return "http://www.asmred.com/".equals(namespaceUri) ? "" : suggestion;
            }
        });

        StringWriter writer = new StringWriter();
//        writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");

        // 序列化对象到 StringWriter
        marshaller.marshal(object, writer);

        // 打印生成的 XML 字符串
        String xmlString = writer.toString();
        xmlString = xmlString.replaceAll("ns2:", "");
        xmlString = xmlString.replaceAll(":ns2", "");
        return xmlString;
    }
}
