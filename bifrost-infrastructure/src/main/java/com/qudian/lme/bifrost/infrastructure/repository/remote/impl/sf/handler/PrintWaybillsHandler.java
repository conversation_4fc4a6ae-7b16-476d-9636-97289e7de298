package com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.api.enums.SfApiCodeEnum;
import com.qudian.lme.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.lme.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.lme.bifrost.api.vo.response.tool.UploadFileRespVO;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.utils.HttpClient;
import com.qudian.lme.bifrost.infrastructure.repository.remote.ToolSrvRemote;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsRequestDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.PrintWaybillsResponseDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf.SfBaseResponseDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.impl.sf.AbstractSfApiTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/28
 */

@Component
@Slf4j
public class PrintWaybillsHandler extends AbstractSfApiTemplate<BatchPrintReqVO, PrintWaybillsRequestDTO, BatchPrintRespVO> {
    @Value("${express.sf.printWaybills.serviceCode:COM_RECE_CLOUD_PRINT_WAYBILLS}")
    private String serviceCode;
    @Value("${express.sf.printWaybills.templateCode:fm_150_standard_YJP2E5E5}")
    private String templateCode;

    @Resource
    private HttpClient httpClient;
    @Resource
    private ToolSrvRemote toolSrvRemote;

    @Override
    public SfApiCodeEnum support() {
        return SfApiCodeEnum.BATCH_PRINT_WAYBILLS;
    }

    @Override
    protected String getServiceCode() {
        return serviceCode;
    }

    @Override
    protected String buildMsgData(PrintWaybillsRequestDTO requestDTO) {
        // 打印面单的参数处理（仅关注当前接口的特殊逻辑）
        requestDTO.setTemplateCode(templateCode);
        requestDTO.setFileType("pdf").setSync(true).setVersion("2.0");
        return JSON.toJSONString(requestDTO);
    }

    @Override
    protected BatchPrintRespVO parseResponse(BatchPrintReqVO reqVO, String responseBody) {
        String requestId = reqVO.getRequestId();
        // 解析打印面单的响应（仅关注当前接口的响应处理）
        SfBaseResponseDTO baseResponse = JSON.parseObject(responseBody, SfBaseResponseDTO.class);
        if (baseResponse == null || StrUtil.isBlank(baseResponse.getApiResultData())) {
            throw new BizException("打印面单响应格式异常, requestID=" + requestId);
        }

        PrintWaybillsResponseDTO printWaybillsResponseDTO = JSON.parseObject(baseResponse.getApiResultData(), PrintWaybillsResponseDTO.class);
        if (printWaybillsResponseDTO == null || !printWaybillsResponseDTO.isSuccess() || printWaybillsResponseDTO.getObj() == null) {
            throw new BizException("打印面单失败, requestID=" + requestId);
        }

        List<BatchPrintRespVO.BatchPrintResp> batchResponse = new ArrayList<>();

        printWaybillsResponseDTO.getObj().getFiles().forEach(file -> {
            String token = file.getToken();
            String fileUrl = file.getUrl();
            try {
                //下载文件
                byte[] bytes = httpClient.download(fileUrl, token);
                UploadFileRespVO uploadFileRespVO = toolSrvRemote.uploadFileV2(bytes, file.getWaybillNo() + ".pdf");
                log.info("op=start_上传 oss 返回={}", JSON.toJSON(uploadFileRespVO));
                String ossUrl =uploadFileRespVO.getData().getSigned_url();
                batchResponse.add(BatchPrintRespVO.BatchPrintResp.builder().pdfObjectKey(uploadFileRespVO.getData().getObject_key())
                        .waybillNo(file.getWaybillNo()).pdfUrl(ossUrl).build());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        return BatchPrintRespVO.builder().batchResponse(batchResponse).build();
    }

    @Override
    protected PrintWaybillsRequestDTO convert(BatchPrintReqVO batchPrintReqVO) {
        List<PrintWaybillsRequestDTO.Document> documentList = new ArrayList<>();
        batchPrintReqVO.getBatchRequest().forEach(t->{
            documentList.add(PrintWaybillsRequestDTO.Document.builder().masterWaybillNo(t.getWaybillNo()).build());
        });
        PrintWaybillsRequestDTO printWaybillsRequestDTO = PrintWaybillsRequestDTO.builder().documents(documentList).build();
        return printWaybillsRequestDTO;
    }

}
