package com.qudian.lme.bifrost.infrastructure.repository.remote.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.api.vo.response.PushSingleTransportResponseVO;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.common.dto.HttpRespDTO;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.log.BizRequestLogParam;
import com.qudian.lme.bifrost.common.utils.HttpClient;
import com.qudian.lme.bifrost.common.utils.common.RedisOperateUtil;
import com.qudian.lme.bifrost.infrastructure.repository.remote.CTTService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.CommonBaseInfoService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.CreateOrderDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.CreateOrderVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.CttShippingStatusReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.CttShippingStatusRespDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.CttTokenRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.ErrorRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.PrintLabelDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.PrintLabelDetailVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.PrintLabelVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ctt.TokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CttServiceImpl implements CTTService {
    @Resource
    private RedisOperateUtil redisOperateUtil;
    private static final String CTT_TOKEN = "ctt_token";
    private static final Long EXPIRE_TIME = 23 * 60 * 60L;
    @Value("${ctt.key:5775j5ie8t2ep1t472av2ta810}")
    private String key;

    @Value("${ctt.secret:bso561j0pp195b6v1keuob0ksk8qf83u8bm3vln2tju8ekd9a4h}")
    private String secret;

    @Value("${ctt.url:https://es-ctt-uat-integration-clients-pool-ids.auth.eu-west-1.amazoncognito.com/}")
    private String url;
    private static final String TOKEN_API = "oauth2/token";
    private static final String LABEL_API = "integrations/trf/labelling/v1.0/shippings/%s/shipping-labels";
    public static final String TRACKING_API = "integrations/trf/item-history-api/history/%s";
    public static final String CANCEL_API = "integrations/manifest/v1.0/rpc-cancel-shipping-by-shipping-code/%s";
    public static final String ORDER_API = "integrations/manifest/v1.0/shippings";
    @Value("${ctt.user.name:user1}")
    private String userName;

    @Value("${ctt.password:passw0rd$.}")
    private String passWord;

    @Value("${ctt.business.url:https://api-test.cttexpress.com/}")
    private String businessUrl;

    /**
     * 查询延迟的时间
     */
    @Value("${ctt.query.time.wait.millis:1000}")
    private Long queryTimeWaitMillis;

    @Value("${ctt.client.center.code:4055400001}")
    private String clientCenterCode;

    @Value("${ctt.shipping.type.code:C24}")
    private String shippingTypeCode;
    @Value("${ctt.token.scope:urn:com:ctt-express:integration-clients:scopes:common/ALL}")
    private String scope;

    @Resource
    private CommonBaseInfoService commonBaseInfoService;
    @Resource
    private HttpClient httpClient;

    @Override
    public void cancelTransport(CancelTransportReqVO reqVO) {
        String orderNumber = reqVO.getThirdOrderNumber();
        String fullUrl = businessUrl + String.format(CANCEL_API, orderNumber);
        HttpRespDTO httpRespDTO = httpClient.postJsonDataForCtt(fullUrl, JSON.toJSONString(new JSONObject()), buildHeader(), new BizRequestLogParam()
                .setCarrier(CarrierTypeEnum.CTT.getName())
                .setForwarderType(CarrierTypeEnum.CTT.getName())
                .setOrderNumbers(Lists.newArrayList(orderNumber)));
        // 解析结果
        parseErrorResp(httpRespDTO);

    }

    private static void parseErrorResp(HttpRespDTO httpRespDTO) {
        if (Objects.isNull(httpRespDTO.getCode()) || httpRespDTO.getCode() > 300) {
            String errorResp = httpRespDTO.getResp();
            ErrorRespVO errorRespVO = JSON.parseObject(errorResp, ErrorRespVO.class);
            throw new BizException(errorRespVO.getError().getErrorCode(), errorRespVO.getError().getErrorDescription());
        }
    }

    @Override
    public PushSingleTransportResponseVO createOrder(PushSingleTransportReqVO reqVO) {
        PushSingleTransportReqVO.RecipientInfo recipientInfo = reqVO.getShipment().getRecipientInfo();
        PushSingleTransportReqVO.SenderInfo senderInfo = reqVO.getShipment().getSenderInfo();
        CreateOrderDTO createOrderDTO = buildOrderInfo(reqVO, recipientInfo, senderInfo);
        HashMap<String, String> header = buildHeader();
        HttpRespDTO res = httpClient.postJsonDataForCtt(businessUrl + ORDER_API, JSON.toJSONString(createOrderDTO), header, new BizRequestLogParam()
                .setCarrier(CarrierTypeEnum.CTT.getName())
                .setForwarderType(CarrierTypeEnum.CTT.getName())
                .setOrderNumbers(Lists.newArrayList(reqVO.getShipment().getOrderNumber())));
        parseErrorResp(res);
        return buildResp(reqVO, res);
    }

    @NotNull
    private CreateOrderDTO buildOrderInfo(PushSingleTransportReqVO reqVO, PushSingleTransportReqVO.RecipientInfo recipientInfo, PushSingleTransportReqVO.SenderInfo senderInfo) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setClientCenterCode(clientCenterCode);
        createOrderDTO.setShippingTypeCode(shippingTypeCode);
        String orderNumber = reqVO.getShipment().getOrderNumber();
        // 设置订单号
        createOrderDTO.setClientReferences(Collections.singletonList(orderNumber));
        // 设置重量
        String weight = commonBaseInfoService.calculateTotalWeight(reqVO);
        createOrderDTO.setShippingWeightDeclared(Double.valueOf(weight));
        createOrderDTO.setItemCount(1);
        // 发件人信息
        createOrderDTO.setSenderName(senderInfo.getSender()).setSenderCountryCode(senderInfo.getSenderCountry())
                .setSenderPostalCode(senderInfo.getSenderPostcode()).setSenderAddress(senderInfo.getSenderAddress())
                .setSenderTown(senderInfo.getSenderCity());
        // 收件人信息
        createOrderDTO.setRecipientName(recipientInfo.getRecipient()).setRecipientCountryCode(recipientInfo.getReceiptCountry())
                .setRecipientPostalCode(recipientInfo.getReceiptPostcode()).setRecipientAddress(recipientInfo.getRecipientAddress())
                .setRecipientTown(recipientInfo.getReceiptCity()).setRecipientPhones(Collections.singletonList(recipientInfo.getRecipientMobilePhone()));
        return createOrderDTO;
    }

    @NotNull
    private static PushSingleTransportResponseVO buildResp(PushSingleTransportReqVO reqVO, HttpRespDTO res) {
        CreateOrderVO createOrderVO = JSON.parseObject(res.getResp(), CreateOrderVO.class);
        PushSingleTransportResponseVO pushSingleTransportResponseVO = new PushSingleTransportResponseVO();
        pushSingleTransportResponseVO.setArticleId(createOrderVO.getShippingData().getShippingCode());
        pushSingleTransportResponseVO.setConsignmentId(createOrderVO.getShippingData().getShippingCode());
        pushSingleTransportResponseVO.setOrderNumber(reqVO.getShipment().getOrderNumber());
        pushSingleTransportResponseVO.setOrigin(reqVO.getOrigin());
        pushSingleTransportResponseVO.setState(reqVO.getWarehouseLocationState());
        return pushSingleTransportResponseVO;
    }

    @Override
    public PrintLabelResponseVO printSingleLabel(SingleOrderTransportReqVO reqVO) {
        String orderNumber = reqVO.getThirdOrderNumber();

        String orderNumberString = String.format(LABEL_API, orderNumber);
        HashMap<String, String> headerMap = buildHeader();
        PrintLabelDTO printLabelDTO = new PrintLabelDTO().setLabelTypeCode("PDF").setModelTypeCode("SINGLE").setLabelOffset(1);

        HttpRespDTO response = httpClient.getRequestForCttWithLog(businessUrl + orderNumberString, printLabelDTO, headerMap, new BizRequestLogParam()
                .setCarrier(CarrierTypeEnum.CTT.getName())
                .setForwarderType(CarrierTypeEnum.CTT.getName())
                .setOrderNumbers(Lists.newArrayList(orderNumber)));
        PrintLabelVO printLabelVO = JSON.parseObject(response.getResp(), PrintLabelVO.class);
        String pdfBase64String = Optional.ofNullable(printLabelVO).map(PrintLabelVO::getData).map(t -> t.get(0))
                .map(PrintLabelDetailVO::getLabel)
                .orElseThrow(() -> new BizException("未获取到对应的pdf，请重试"));
        return commonBaseInfoService.parseBase64PdfAndUpload(pdfBase64String);
    }

    @NotNull
    private HashMap<String, String> buildHeader() {
        String token = getToken();
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("user_name", userName);
        headerMap.put("password", passWord);
        headerMap.put("Authorization", token);
        return headerMap;
    }

    @Override
    public TransportTrackRespVO shippingStatus(ShippingStatusReqDTO reqVO) {
        // warning!! 由于下游不支持批量查询，这里只能for循环查
        TransportTrackRespVO transportTrackRespVO = new TransportTrackRespVO();
        List<TransportTrackRespVO.TrackingResult> results = new ArrayList<>();
        for (ShippingStatusReqDTO.ShippingOrderInfo order : reqVO.getOrderInfoList()) {
            try {
                TransportTrackRespVO.TrackingResult trackingResult = querySingleShippingStatus(order);
                results.add(trackingResult);
                // 防止触发限流
                TimeUnit.MILLISECONDS.sleep(queryTimeWaitMillis);
            } catch (Exception e) {
                log.error("orderInfo:{}, query gls shipping status fail, exception:", order, e);
            }
        }
        transportTrackRespVO.setTrackingResults(results);
        return transportTrackRespVO;
    }

    private TransportTrackRespVO.TrackingResult querySingleShippingStatus(ShippingStatusReqDTO.ShippingOrderInfo orderInfo) {
        String token = getToken();
        String fullUrl = String.format(businessUrl + TRACKING_API, orderInfo.getTrackingId());
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", token);
        CttShippingStatusReqDTO apitrack = new CttShippingStatusReqDTO()
                .setView("APITRACK")
                .setShowItems(false);

        HttpRespDTO requestReturnDTOWithLog = httpClient.getRequestForCttWithLog(fullUrl, apitrack, headerMap, new BizRequestLogParam()
                .setCarrier(CarrierTypeEnum.CTT.getName())
                .setOrderNumbers(Lists.newArrayList(orderInfo.getOrderId()))
                .setForwarderType(CarrierTypeEnum.CTT.getName()));
        String resp = requestReturnDTOWithLog.getResp();
        if (StringUtils.isEmpty(resp)) {
            throw new BizException("ctt track return null");
        }
        JSONObject jsonObject = JSON.parseObject(resp);
        CttShippingStatusRespDTO dto = jsonObject.getObject("data", CttShippingStatusRespDTO.class);
        if (Objects.isNull(dto)) {
            throw new BizException("ctt track return null");
        }
        TransportTrackRespVO.TrackingResult shippingStatusDTO = new TransportTrackRespVO.TrackingResult()
                .setArticleId(orderInfo.getTrackingId())
                .setOrderNumber(orderInfo.getOrderId())
                .setCarrier(CarrierTypeEnum.CTT.getName());

        List<TransportTrackRespVO.TrackingResult.Event> eventList = dto.getShippingHistory().getEvents().stream()
                .map(event -> {
                    Date date = convertStringToDate(event.getEventDate());
                    return new TransportTrackRespVO.TrackingResult.Event()
                            .setDesc(event.getDescription())
                            .setEventTime(date.getTime())
                            .setDate(DateUtil.formatDateTime(date))
                            .setEventCode(event.getCode());
                })
                .sorted(Comparator.comparing(TransportTrackRespVO.TrackingResult.Event::getEventTime).reversed())
                .collect(Collectors.toList());
        shippingStatusDTO.setEvents(eventList);
        // 取最新的事件
        if (CollectionUtils.isNotEmpty(eventList)) {
            shippingStatusDTO.setStatus(eventList.get(0).getEventCode());
        }
        return shippingStatusDTO;
    }

    @Override
    public String getToken() {
        // 尝试从redis 获取 如果获取不到，请求ctt 存入redis 响应
        String token = redisOperateUtil.get(CTT_TOKEN);
        log.info("从redis的获取结果为{}", token);
        if (StringUtils.isBlank(token)) {
            // 请求CTT
            TokenDTO tokenDTO = new TokenDTO().setClientId(key).setClientSecret(secret).setScope(scope).
                    setGrantType("client_credentials");
            String urlEncodedString = toUrlEncodedString(tokenDTO);
            String s = httpClient.postForm(url + TOKEN_API, urlEncodedString, new HashMap<>(), new BizRequestLogParam()
                    .setCarrier(CarrierTypeEnum.CTT.getName())
                    .setForwarderType(CarrierTypeEnum.CTT.getName()));
            CttTokenRespVO cttTokenRespVO = JSON.parseObject(s, CttTokenRespVO.class);
            if (Objects.isNull(cttTokenRespVO) || StringUtils.isBlank(cttTokenRespVO.getAccessToken()) || Objects.isNull(cttTokenRespVO.getTokenType())) {
                throw new BizException("ctt token 获取失败，请重试");
            }
            token = cttTokenRespVO.getTokenType() + " " + cttTokenRespVO.getAccessToken();
            redisOperateUtil.setIfAbsent(CTT_TOKEN, token, EXPIRE_TIME);
        }
        return token;
    }

    public String toUrlEncodedString(TokenDTO tokenDTO) {
        try {
            return "client_id=" + URLEncoder.encode(tokenDTO.getClientId(), StandardCharsets.UTF_8.toString()) +
                    "&client_secret=" + URLEncoder.encode(tokenDTO.getClientSecret(), StandardCharsets.UTF_8.toString()) +
                    "&scope=" + URLEncoder.encode(tokenDTO.getScope(), StandardCharsets.UTF_8.toString()) +
                    "&grant_type=" + URLEncoder.encode(tokenDTO.getGrantType(), StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            log.error("拼接参数失败", e);
        }
        throw new BizException("请重试");
    }

    public static Date convertStringToDate(String dateString) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date date = null;
        try {
            date = formatter.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }
}
