package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.speed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class PrintLabelDTO implements Serializable {
    /**
     * 标签类型
     */
    private String type;

    /**
     * 标签url
     */
    private String url;


    /**
     * 客户参考号
     */
    @JSONField(name = "reference_no")
    private String referenceNo;

}
