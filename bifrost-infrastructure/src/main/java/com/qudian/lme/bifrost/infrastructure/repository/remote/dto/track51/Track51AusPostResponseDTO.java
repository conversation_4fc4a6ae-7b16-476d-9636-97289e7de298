package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.track51;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Track51AusPostResponseDTO {
    /**
     * 澳洲邮政订单号
     */
    private String tracking_number;
    private String courier_code;
    /**
     * LME 订单号
     */
    private String order_number;
    private String delivery_status;
    private boolean archived;
    private boolean updating;
    private Date order_create_time;
    private Date created_at;
    private Date update_date;
    private String shipping_date;
    private String customer_name;
    private String customer_email;
    private String customer_phone;
    private String title;
    private String logistics_channel;
    private String note;
    private String destination;
    private String original;
    private String service_code;
    private String weight;
    private String substatus;
    private String status_info;
    private String previously;
    private String destination_track_number;
    private String exchangeNumber;
    private String consignee;
    private String scheduled_delivery_date;
    private String Scheduled_Address;
    private String latest_event;
    private Date lastest_checkpoint_time;
    private int transit_time;
    private int stay_time;
    private Origin_info origin_info;
    private Destination_info destination_info;

    @Data
    public static class Trackinfo {
        /**
         * 节点时间
         */
        private Date checkpoint_date;
        /**
         * 节点描述
         */
        private String tracking_detail;
        private String location;
        private String checkpoint_delivery_status;
        private String checkpoint_delivery_substatus;
    }

    @Data
    public static class Origin_info {
        private String courier_code;
        private String courier_phone;
        private String weblink;
        private String reference_number;
        private Date received_date;
        private String dispatched_date;
        private String departed_airport_date;
        private String arrived_abroad_date;
        private String customs_received_date;
        private String arrived_destination_date;
        private List<Trackinfo> trackinfo;
    }

    @Data
    public static class Destination_info {
        private String courier_code;
        private String courier_phone;
        private String weblink;
        private String reference_number;
        private Date received_date;
        private String dispatched_date;
        private String departed_airport_date;
        private String arrived_abroad_date;
        private String customs_received_date;
        private String arrived_destination_date;
        private List<Trackinfo> trackinfo;
    }
}
