package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class CreateOrderDTO implements Serializable {
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String city;
    private String state;
    @JSONField(name = "Suburb")
    private String suburb;
    private String postcode;
    private String country;
    private String shipperName;
    private String recipientName;
    private String email;
    private String phone;
    private String referenceNo;
    private String serviceType;
    private String finalMileInjectionLocation;
    private String goodsDescription;
    private Double goodsValue;
    private String dimensionUnit;
    private String weightUnit;
    private String instruction;
    private Boolean tailLiftService;
    private Boolean insuranceRequired;
    private Boolean authorityToLeave;
    private Boolean saveDropEnabled;
    private Boolean containsDangerousGoods;
    private Boolean allowPartialDelivery;
    private List<OrderItem> orderItems;

    @Data
    public static class OrderItem{
        private String description;
        private Integer itemCount;
        private Double unitValue;
        private Double weight;
        private Integer height;
        private Integer length;
        private Integer width;

    }
}
