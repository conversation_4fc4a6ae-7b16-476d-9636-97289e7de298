package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.exp;

import lombok.Getter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@Getter
public  class GetExpCliResult {
    @XmlElementWrapper(name = "expediciones" ,namespace = "")
    @XmlElement(name = "exp")
    private List<Exp> expList;
}
