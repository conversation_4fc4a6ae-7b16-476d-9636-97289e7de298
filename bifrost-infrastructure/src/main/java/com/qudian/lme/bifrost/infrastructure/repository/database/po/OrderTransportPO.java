package com.qudian.lme.bifrost.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 订单转运状态流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bifrost_order_transport")
@ApiModel(value="OrderTransport对象", description="订单转运状态流程表")
public class OrderTransportPO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "转运订单号")
    private String orderNumber;

    @ApiModelProperty(value = "追踪编号")
    private String articleId;

    @ApiModelProperty(value = "运单号")
    private String consignmentId;

    @ApiModelProperty(value = "运单PDF地址")
    private String pdfUrl;

    @ApiModelProperty(value = "转运商类型")
    private String forwarderType;

    /**
     * 承运商
     */
    @ApiModelProperty(value = "承运商")
    private String carrier;

    @ApiModelProperty(value = "运单状态，1、转运下单，2、打印面单，3、澳邮转运，4、取消转运，5、转运签收")
    private Integer status;

    @ApiModelProperty(value = "面单在文件系统中的相对路径")
    private String objectKey;

    @ApiModelProperty(value = "面单在文件系统中的绝对路径")
    private String signedUrl;

    @ApiModelProperty(value = "转运商状态")
    private String forwarderStatus;

    @ApiModelProperty(value = "是否删除 1已删除 0 未删除")
    private Integer deleteFlag;

    private Date createdTime;

    private Date updatedTime;

    /**
     * 完成时间(终态时间)
     */
    private Date finishTime;

    /**
     * 仓库所在州
     */
    private String state;


    /**
     * 渠道 因为会有特殊的账号
     */
    private String channel;


    private String origin;


}
