package com.qudian.lme.bifrost.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 请求日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bifrost_request_log")
public class RequestLogPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * trace_id
     */
    private String traceId;

    /**
     * 响应状态 1 失败 0 成功
     */
    private Integer status;

    /**
     * 转运商类型
     */
    private String forwarderType;

    /**
     * 承运商
     */
    private String carrier;

    /**
     * 关联的运单id列表
     */
    private String orderNumbers;

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 请求body
     */
    private String requestBody;

    /**
     * 响应
     */
    private String response;

    /**
     * 是否删除 1已删除 0 未删除
     */
    private Integer deleteFlag;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;


}
