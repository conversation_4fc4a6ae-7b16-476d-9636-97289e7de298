package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "EtiquetaEnvio", namespace = "http://www.asmred.com/")
@XmlAccessorType(XmlAccessType.FIELD)
public class EtiquetaEnvio {
    private String uidCliente;
    private String codigo;
    private String tipoEtiqueta;
}
