package com.qudian.lme.bifrost.infrastructure.repository.remote.dto.sf;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/26
 */

// com.sf.express.model.request.CreateOrderRequest.java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CreateOrderRequestDTO {

    //必填、响应报文的语言， 缺省值为zh-CN，目前支持以下值zh-CN 表示中文简体， zh-TW或zh-HK或 zh-MO表示中文繁体， en表示英文
    private String language;

    //必填、客户订单号， 客户订单号在客户系统中是唯一的， 客户订单号在客户系统中是唯一的， 客户订单号在客户系统中是唯一的
    private String orderId;

    // 必填 托寄物信息
    @JSONField(name = "cargoDetails")
    private List<CargoDetail> cargoDetails;


    // 必填 收寄件人信息（0=寄件人，1=收件人，官网必传）
    @JSONField(name = "contactInfoList")
    private List<ContactInfo> contactInfoList;



    // 必填 快件产品类别，支持附录《快件产品类别表》的产品编码值，仅可使用与顺丰销售约定的快件产品。 https://open.sf-express.com/developSupport/734349?activeIndex=324604
    // 1 顺丰特快 2 顺丰标快 默认 1
    @JSONField(name = "expressTypeId")
    private String expressTypeId;

    //默认值 接收到报文的时间，要求上门取件开始时间， 格式： YYYY-MM-DD HH24:MM:SS， 示例： 2012-7-30 09:30:00 ，若该字段没有赋值，默认开始时间为当前时间，（可配合上门取件截止时间pickupAppointEndTime扩展字段备注进行下发，若没有给截止时间则系统默认1小时的截止时间）
    private String sendStartTm;


    //是否返回路由标签： 默认1， 1：返回路由标签， 0：不返回；除部分特殊用户外，其余用户都默认返回
    private Integer isReturnRoutelabel;


    //付款方式，支持以下值： 1:寄方付 2:收方付 3:第三方付 默认 1
    @JSONField(name = "payMethod")
    private Integer payMethod;

    // 备注（可选）
    @JSONField(name = "remark")
    private String remark;
    //拖寄物类型描述,如： 文件，电子产品，衣服等，  托寄物名称请使用cargoDetails传值
    private String cargoDesc;

    // 内部类：货物信息（补充必填字段）
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Accessors(chain = true)
    public static class CargoDetail {

        private double amount;
        private double count;
        private String currency;
        private String goodPrepardNo;
        private String hsCode;
        //货物名称，如果需要生成电子 运单，则为必填
        private String name;
        private String productRecordNo;
        private String sourceArea;
        private String taxNo;
        private String unit;
        private double weight;
    }


    // 内部类：收寄件人信息（补充必填字段）
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    @Builder
    public static class ContactInfo {

        // 必填 详细地址，若有四级行政区划，如镇/街道等信息可拼接至此字段，格式样例：镇/街道+详细地址。若province/city 字段的值不传，此字段必须包含省市信息，避免影响原寄地代码识别，如：广东省深圳市福田区新洲十一街万基商务大厦10楼；此字段地址必须详细，否则会影响目的地中转识别；
        private String address;
        private String city;
        private String contact;
        // 必填 地址类型： 1，寄件方信息 2，到件方信息
        private int contactType;
        //必填 国家或地区代码 例如：内地件CN 香港852
        private String country;
        private String county;
        private String mobile;
        private String postCode;
        private String province;
        private String tel;
    }


}