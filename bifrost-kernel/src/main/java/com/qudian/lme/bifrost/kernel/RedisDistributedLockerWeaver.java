package com.qudian.lme.bifrost.kernel;

import com.qudian.lme.bifrost.common.exception.FrequencyRequestException;
import com.qudian.lme.bifrost.common.utils.common.RedisOperateUtil;
import com.qudian.lme.bifrost.common.utils.middleware.SpelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * <p>文件名称: com.qudian.qcai.society.infrastructure.common.aspect.RedisDistributedLockerAspect</p>
 * <p>文件描述: 分布式锁切面</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/6/21
 */
@Aspect
@Component
@Slf4j
@Order(Ordered.LOWEST_PRECEDENCE-99)
public class RedisDistributedLockerWeaver {
    @Resource
    private RedisOperateUtil operateTool;

    public static final String REDIS_DISTRIBUTE_LOCK = "distribute_lock_%s_%s";
    @Around("@annotation(locker)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLocker locker) throws Throwable {
        log.info("[distributed_aspect] locker：{}", locker.name());
        String key = this.getRedisKey(joinPoint, locker.name(),locker.key());
        Object result = null;
        long millis = locker.unit().toMillis(locker.expire());
        if (operateTool.spinLock(key, key, millis)) {
            log.info("[distributed_aspect] key：{} spin_lock successfully passed.", key);
            try {
                result = joinPoint.proceed();
            } finally {
                operateTool.releaseDistributedLock(key, key);
            }
        } else {
            log.warn("[distributed_aspect]key：{} spin_lock be occupied or timeout failed.", key);
            throw new FrequencyRequestException("Requests are too frequent,please try again later.");
        }
        return result;
    }

    private String getRedisKey(ProceedingJoinPoint joinPoint,String name, String spel) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = methodSignature.getMethod();
        //兜底 中间key 类名加方法名
        String middleKey = StringUtils.isBlank(name) ? joinPoint.getTarget().getClass().getName() + "." + targetMethod.getName()
                : name;
        return String.format(REDIS_DISTRIBUTE_LOCK, middleKey, SpelUtil.parse(joinPoint.getTarget(), spel, targetMethod, joinPoint.getArgs(), String.class));
    }
}
