package com.qudian.lme.bifrost.server.weaver;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.common.constant.AnnotatedComponentOrderConstant;
import com.qudian.lme.bifrost.common.enums.ExceptionEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.apache.logging.log4j.ThreadContext;
import org.apache.skywalking.apm.toolkit.trace.ActiveSpan;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.lme.driver.server.weaver.FacadeWeaver</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/28
 */
@Order(AnnotatedComponentOrderConstant.FACADE_WEAVER)
@Aspect
@Component
@Slf4j
public class FacadeWeaver {

    private static final Long MAX_TIME = 4000L;
    @Pointcut("execution(* com.qudian.lme.bifrost.server.facade..*(..))")
    public void exPointCut() {
    }

    @Around("exPointCut()")
    public Object around(ProceedingJoinPoint point) {
        long startTime = System.currentTimeMillis();
        //附加日志上下文信息
        addExtraLogMsg();
        String argString = getArgsInfo(point);
        Signature sig = point.getSignature();
        String signature = sig.getName();
        try {
            log.info("[entrance_args] signature:{}, args:{}", sig.toShortString(), argString);
            ActiveSpan.tag("args", argString);
        } catch (Exception e) {
            //nothing to do passing.
        }
        Object proceed = null;
        Throwable throwable = null;
        try {
            proceed = point.proceed();
            try {
                log.info("[entrance_result] {}", JSONObject.toJSONString(proceed));
            } catch (Exception e) {
                //noting to do passing.
            }
            return proceed;
        } catch (Throwable t) {
            throwable = t;
            log.error("interface error, args: {}，throwable: {}, trace:{}", argString, t, Arrays.stream(t.getStackTrace()).map(f -> "\tat " + f).collect(Collectors.joining("\n")));
            alarm(signature, t);
            proceed = this.handleException(t);
            return proceed;
        } finally {
            long elapsedMills = System.currentTimeMillis() - startTime;
            log.info(signature + "接口执行消耗{}ms ", elapsedMills);
            if (elapsedMills > MAX_TIME) {
                log.error(signature + "接口执行消耗超过"+ MAX_TIME/1000 +"秒："+elapsedMills +"ms");
            }
        }
    }

    private Object handleException(Throwable t) {
        BaseResponseDTO<Object> res = ResponseBuilder.buildSystemError();
        if (t instanceof BizException) {
            BizException bizException = (BizException) t;
            res.setCode(bizException.getCode());
            res.setMessage(bizException.getMessage());
        } else if (t instanceof ConstraintViolationException) {
            ConstraintViolationException constraintViolationException = (ConstraintViolationException) t;
            res.setCode(ExceptionEnum.PARAMETER_VERIFICATION.getCode());
            res.setMessage(constraintViolationException.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("；")));
        } else if (t instanceof RpcException) {
            RpcException rpcException = (RpcException) t;
            res.setCode(ExceptionEnum.RPC_INVOKE.getCode());
            res.setMessage(ExceptionEnum.RPC_INVOKE.getMsg());
        } else {
            res.setCode(ExceptionEnum.SYSTEM_ERR.getCode());
            res.setMessage(t.getMessage());
        }
        return res;
    }

    /**
     * 报警扩展口
     * @param signature
     * @param throwable
     */
    protected void alarm(String signature, Throwable throwable) {
    }

    private String getArgsInfo(ProceedingJoinPoint joinPoint) {
        try {
            return JSON.toJSONString(joinPoint.getArgs());
        } catch (Exception e) {
            return "";
        }
    }

    private void addExtraLogMsg() {
        try {
            ThreadContext.put("X-TRACE-ID", TraceContext.traceId());
        } catch (Exception e) {
            log.info("X-TRACE-ID get error: {}", e.getMessage());
        }
    }

}
