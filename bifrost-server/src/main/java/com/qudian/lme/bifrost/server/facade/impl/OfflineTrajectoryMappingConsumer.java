package com.qudian.lme.bifrost.server.facade.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qudian.lme.bifrost.api.vo.request.ExpressDeliveryOrderInfo;
import com.qudian.lme.bifrost.api.vo.request.OfflineTrajectoryMappingReqDTO;
import com.qudian.lme.bifrost.application.service.OrderTransportService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.Track51RemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import com.qudian.lme.oms.api.dto.BaseMqDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.qudian.lme.bifrost.common.enums.TransportStatusEnum.DISPATCH_SHIPMENTS;

@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.consumer[1].topic}",
        consumerGroup = "${rocketmq.consumer[1].group}", consumeThreadNumber = 1)
public class OfflineTrajectoryMappingConsumer implements RocketMQListener<BaseMqDto<String>> {

    @Autowired
    private OrderTransportService orderTransportService;
    @Autowired
    private OfflineTrajectoryMappingConsumer myself;
    @Resource
    private Track51RemoteService track51RemoteService;

    @Override
    public void onMessage(BaseMqDto<String> message) {
        String payload = message.getPayload();
        if (ObjectUtils.isEmpty(payload)) {
            return;
        }
        OfflineTrajectoryMappingReqDTO offlineTrajectoryMappingReqDTO = JSON.parseObject(payload, OfflineTrajectoryMappingReqDTO.class);
        if (Objects.isNull(offlineTrajectoryMappingReqDTO) || CollectionUtils.isEmpty(offlineTrajectoryMappingReqDTO.getExpressDeliveryOrderInfoList())) {
            return;
        }
        List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfoList = offlineTrajectoryMappingReqDTO.getExpressDeliveryOrderInfoList();
        Collections.reverse(expressDeliveryOrderInfoList);
        List<ExpressDeliveryOrderInfo> distinctList = expressDeliveryOrderInfoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExpressDeliveryOrderInfo::getTransportNo))), ArrayList::new));
        // 订阅
        track51RemoteService.subscribeTrace(distinctList);
        // 首先查询
        LambdaQueryWrapper<OrderTransportPO> orderTransportPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderTransportPOLambdaQueryWrapper.in(OrderTransportPO::getOrderNumber, expressDeliveryOrderInfoList.stream().map(ExpressDeliveryOrderInfo::getTransportNo).collect(Collectors.toSet()));
        orderTransportPOLambdaQueryWrapper.eq(OrderTransportPO::getDeleteFlag, 0);
        List<OrderTransportPO> orderTransportPOList = orderTransportService.getBaseMapper().selectList(orderTransportPOLambdaQueryWrapper);

        List<ExpressDeliveryOrderInfo> needInsert = distinctList.stream().filter(t -> !orderTransportPOList.stream().map(OrderTransportPO::getOrderNumber).collect(Collectors.toList()).contains(t.getTransportNo())).collect(Collectors.toList());
        List<OrderTransportPO> needReinsertAndDelete = orderTransportPOList.stream().filter(t -> distinctList.stream().map(ExpressDeliveryOrderInfo::getTransportNo).collect(Collectors.toList()).contains(t.getOrderNumber()) && t.getUpdatedTime().before(offlineTrajectoryMappingReqDTO.getUpdateTime())).collect(Collectors.toList());
        Map<String, ExpressDeliveryOrderInfo> map = distinctList.stream().collect(Collectors.toMap(ExpressDeliveryOrderInfo::getTransportNo, v -> v, (k1, k2) -> k1));
        // 找出db存在的和不存在的 如果全部不存在，所有的直接写入
        if (CollectionUtils.isNotEmpty(needInsert)) {
            // 调用写入接口
            batchInsert(offlineTrajectoryMappingReqDTO, needInsert);
        }

        if (CollectionUtils.isNotEmpty(needReinsertAndDelete)) {
            myself.batchDeleteAndInsert(needReinsertAndDelete,map,offlineTrajectoryMappingReqDTO);
        }
    }

    @Transactional
    public void batchDeleteAndInsert(List<OrderTransportPO> orderTransportPOList,Map<String, ExpressDeliveryOrderInfo> map,OfflineTrajectoryMappingReqDTO offlineTrajectoryMappingReqDTO) {
        LambdaUpdateWrapper<OrderTransportPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(OrderTransportPO::getId, orderTransportPOList.stream().map(OrderTransportPO::getId).collect(Collectors.toList())).set(OrderTransportPO::getDeleteFlag, 1);
        orderTransportService.getBaseMapper().update(null, lambdaUpdateWrapper);
        List<OrderTransportPO> newOrderTransportList = orderTransportPOList.stream().map(t -> buildOrderTransportPO(offlineTrajectoryMappingReqDTO, map.get(t.getOrderNumber()), t.getFinishTime())).collect(Collectors.toList());
        orderTransportService.saveBatch(newOrderTransportList);
    }
    private void batchInsert(OfflineTrajectoryMappingReqDTO offlineTrajectoryMappingReqDTO, List<ExpressDeliveryOrderInfo> expressDeliveryOrderInfoList) {

        List<OrderTransportPO> orderTransportPOList = expressDeliveryOrderInfoList.stream().map(t -> buildOrderTransportPO(offlineTrajectoryMappingReqDTO, t,null)).collect(Collectors.toList());
        orderTransportService.saveBatch(orderTransportPOList);
    }

    @NotNull
    private static OrderTransportPO buildOrderTransportPO(OfflineTrajectoryMappingReqDTO offlineTrajectoryMappingReqDTO, ExpressDeliveryOrderInfo t,Date finishTime) {
        OrderTransportPO orderTransportPO = new OrderTransportPO();
        orderTransportPO.setOrderNumber(t.getTransportNo());
        orderTransportPO.setArticleId(t.getTransportLabelNo());
        orderTransportPO.setStatus(DISPATCH_SHIPMENTS.getCode());
        orderTransportPO.setOrigin(offlineTrajectoryMappingReqDTO.getOrigin());
        orderTransportPO.setCarrier(Strings.EMPTY);
        orderTransportPO.setUpdatedTime(offlineTrajectoryMappingReqDTO.getUpdateTime());
        orderTransportPO.setDeleteFlag(0);
       if (Objects.nonNull(finishTime)){
           orderTransportPO.setFinishTime(finishTime);
       }else {
           orderTransportPO.setFinishTime(new Date());
       }
        return orderTransportPO;
    }
}
