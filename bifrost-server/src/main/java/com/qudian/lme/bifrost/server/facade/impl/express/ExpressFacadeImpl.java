package com.qudian.lme.bifrost.server.facade.impl.express;

import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.facade.express.ExpressFacade;
import com.qudian.lme.bifrost.api.vo.request.express.BatchPrintReqVO;
import com.qudian.lme.bifrost.api.vo.request.express.CreateExpressReqVO;
import com.qudian.lme.bifrost.api.vo.response.express.BatchPrintRespVO;
import com.qudian.lme.bifrost.api.vo.response.express.CreateExpressRespVO;
import com.qudian.lme.bifrost.application.service.ExpressService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@RestController
@Slf4j
@Service(version = "1.0.0")
@Api(tags = "ExpressFacadeImpl")
@Validated
public class ExpressFacadeImpl implements ExpressFacade {

    @Resource
    private ExpressService expressService;

    @Override
    @PostMapping("/tms/express/create")
    public BaseResponseDTO<CreateExpressRespVO> create(@RequestBody CreateExpressReqVO createExpressReqVO) {
        log.info("op=start_ExpressFacadeImpl.create, createExpressReqVO={}", createExpressReqVO);
        return ResponseBuilder.buildSuccess(expressService.create(createExpressReqVO));

    }

    @Override
    @PostMapping("/tms/express/batchPrint")
    public BaseResponseDTO<BatchPrintRespVO> batchPrint(@RequestBody BatchPrintReqVO batchPrintReqVO) {
        log.info("op=start_ExpressFacadeImpl.create, createExpressReqVO={}", batchPrintReqVO);
        return ResponseBuilder.buildSuccess(expressService.batchPrint(batchPrintReqVO));
    }


}
