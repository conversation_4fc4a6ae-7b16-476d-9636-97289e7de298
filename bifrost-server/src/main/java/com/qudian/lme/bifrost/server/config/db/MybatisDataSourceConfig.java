package com.qudian.lme.bifrost.server.config.db;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.qudian.lme.bifrost.infrastructure.handler.DbEnumHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Optional;

@Configuration
@MapperScan(basePackages = {MybatisDataSourceConfig.PACKAGE}, sqlSessionFactoryRef = "mybatisSqlSessionFactory")
@EnableTransactionManagement
@Slf4j
public class MybatisDataSourceConfig {
    protected static final String PACKAGE = "com.qudian.lme.bifrost.infrastructure.repository.database.mapper";

    @Bean(name = "mybatisDataSource")
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource mybatisDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "mybatisSqlSessionFactory")
    public SqlSessionFactory permissionsDataSourceSqlSessionFactory(@Qualifier("mybatisDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/**/*Mapper.xml"));
        Optional.ofNullable(bean.getObject()).ifPresent(s -> s.getConfiguration().setMapUnderscoreToCamelCase(true));
        Optional.ofNullable(bean.getObject()).ifPresent(s -> s.getConfiguration().setDefaultEnumTypeHandler(DbEnumHandler.class));
        Optional.ofNullable(bean.getObject()).ifPresent(s -> s.getConfiguration().setJdbcTypeForNull(JdbcType.NULL));
        return bean.getObject();
    }

    @Bean(name = "mybatisTransactionManager")
    public DataSourceTransactionManager permissionsTransactionManager(@Qualifier("mybatisDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "mybatisSqlSessionTemplate")
    public SqlSessionTemplate permissionsSqlSessionTemplate(@Qualifier("mybatisSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
