package com.qudian.lme.bifrost.server.facade.impl;

import com.qudian.java.components.base.builder.ResponseBuilder;
import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.facade.TransportTrackFacade;
import com.qudian.lme.bifrost.api.vo.request.TransportTrackReqVO;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.service.TransportTrackService;
import com.qudian.lme.bifrost.infrastructure.annotation.DubboService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * {@inheritDoc} {@link com.qudian.lme.bifrost.api.facade.TransportTrackFacade}impl
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
@DubboService
@Slf4j
public class TransportTrackFacadeImpl implements TransportTrackFacade {

    @Resource
    private TransportTrackService transportTrackService;

    @Override
    public BaseResponseDTO<TransportTrackRespVO> queryTransportTrack(TransportTrackReqVO reqVO) {
        return ResponseBuilder.buildSuccess(transportTrackService.queryTransportTrack(reqVO));
    }
}
