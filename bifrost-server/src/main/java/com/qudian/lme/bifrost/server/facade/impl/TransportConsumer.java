package com.qudian.lme.bifrost.server.facade.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.application.mq.dto.CaiNiaoTransportDTO;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import com.qudian.lme.oms.api.dto.BaseMqDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static com.qudian.lme.bifrost.common.enums.TransportStatusEnum.DISPATCH_SHIPMENTS;

@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.consumer[0].topic}",
        selectorExpression = "${rocketmq.consumer[0].selector-expression}",
        consumerGroup = "${rocketmq.consumer[0].group}")
public class TransportConsumer implements RocketMQListener<BaseMqDto<String>> {

    @Autowired
    private OrderTransportMapper orderTransportMapper;
    @Autowired
    private TransportConsumer myself;

    @Override
    public void onMessage(BaseMqDto<String> message) {
        String payload = message.getPayload();
        if (ObjectUtils.isEmpty(payload)) {
            return;
        }
        CaiNiaoTransportDTO caiNiaoTransportDTO = JSON.parseObject(payload, CaiNiaoTransportDTO.class);
        // 首先查询
        LambdaQueryWrapper<OrderTransportPO> orderTransportPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderTransportPOLambdaQueryWrapper.eq(OrderTransportPO::getOrderNumber, caiNiaoTransportDTO.getTransportNo());
        orderTransportPOLambdaQueryWrapper.eq(OrderTransportPO::getDeleteFlag,0);
        List<OrderTransportPO> orderTransportPOList = orderTransportMapper.selectList(orderTransportPOLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(orderTransportPOList)) {
            // 调用写入接口
            buildAndInsert(caiNiaoTransportDTO, null);
        } else {
            if (orderTransportPOList.stream().anyMatch(t -> caiNiaoTransportDTO.getUpdateTime().after(t.getUpdatedTime())
                    && t.getOrderNumber().equals(caiNiaoTransportDTO.getTransportNo()))) {
                // 设置之前的为false
                try {
                    // 因为产品业务逻辑上没法保证是否会出现已经转运的是否会被刷新，所以只能代码保证finishTime一定只会插入一次
                    OrderTransportPO orderTransportPO = orderTransportPOList.get(0);
                    myself.deleteOldAndInsertNew(caiNiaoTransportDTO, orderTransportPO.getFinishTime());
                } catch (Exception e) {
                    log.error("插入db失败", e);
                }
            } else {
                log.info("传了更早的数据，数据为{}", message);
            }
            // 设置之前所有的为false
        }
    }

    @Transactional
    public void deleteOldAndInsertNew(CaiNiaoTransportDTO caiNiaoTransportDTO, Date finishTime) {
        LambdaUpdateWrapper<OrderTransportPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(OrderTransportPO::getOrderNumber, caiNiaoTransportDTO.getTransportNo()).set(OrderTransportPO::getDeleteFlag, 1);
        orderTransportMapper.update(null, lambdaUpdateWrapper);
        buildAndInsert(caiNiaoTransportDTO, finishTime);
    }

    private void buildAndInsert(CaiNiaoTransportDTO caiNiaoTransportDTO, Date finishTime) {
        OrderTransportPO orderTransportPO = new OrderTransportPO();
        orderTransportPO.setOrderNumber(caiNiaoTransportDTO.getTransportNo());
        orderTransportPO.setArticleId(caiNiaoTransportDTO.getTransportLabelNo());
        orderTransportPO.setStatus(DISPATCH_SHIPMENTS.getCode());
        orderTransportPO.setCarrier(ForwarderTypeEnum.CAI_NIAO.getName());
        orderTransportPO.setUpdatedTime(caiNiaoTransportDTO.getUpdateTime());
        if (finishTime == null){
            orderTransportPO.setFinishTime(new Date());
        } else {
            orderTransportPO.setFinishTime(finishTime);
        }
        orderTransportMapper.insert(orderTransportPO);
    }
}
