package com.qudian.lme.bifrost.server.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.application.factory.transportTrack.BaseTransportTrackWorker;
import com.qudian.lme.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTraceAnalyzeLogMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTraceAnalyzeLogPO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import com.qudian.lme.bifrost.server.job.param.TransportTraceSyncJobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.qudian.lme.bifrost.common.enums.TransportStatusEnum.TRACK_SYNC_STATUS_LIST;

/**
 * {@inheritDoc} 物流轨迹同步任务
 *
 * <AUTHOR>
 * @since 2023/8/1
 **/
@Component
@Slf4j
public class TransportTraceSyncJob {
    @Resource
    private OrderTraceAnalyzeLogMapper analyzeLogMapper;
    @Resource
    private OrderTransportMapper orderTransportMapper;
    @Resource
    private TransportTrackWorkerFactory transportSyncWorkerFactory;

    @Value("${transport.track.query.order.partition.size:200}")
    private Integer queryOrderPartitionSize;
    @Value("${transport.track.task.partition.size:10}")
    private Integer taskPartitionSize;
    @Value("${transport.track.task.query.remote.retry.time:3}")
    private Integer queryRemoteRetryTime;

    @XxlJob("transportTraceSyncJob")
    public ReturnT<String> process(String param) {
        if (StringUtils.isBlank(param)) {
            for (CarrierTypeEnum value : CarrierTypeEnum.values()) {
                processByCarrier(value.getName());
            }
            // 临时加上菜鸟的线下转运
            processByCarrier(ForwarderTypeEnum.CAI_NIAO.getName());
            // 处理为空的
            processByCarrier("");
            return ReturnT.SUCCESS;
        }
        // 指定服务商类同步或运单同步
        TransportTraceSyncJobParam jobParam = JSON.parseObject(param, TransportTraceSyncJobParam.class);
        if (Objects.nonNull(jobParam.getCarrier())) {
            processByCarrier(jobParam.getCarrier());
        } else {
            retryByOrders(jobParam.getOrderNums());
        }
        return ReturnT.SUCCESS;
    }

    public void processByCarrier(String carrier) {
        log.info("carrier:{}, transport track sync begin------", carrier);
        BaseTransportTrackWorker worker = transportSyncWorkerFactory.getWorker(carrier);

        // todo 是不是查询20天内的，最多 查询所有id，状态为未完成的
        List<Long> ids = orderTransportMapper.selectIdByStatusListAndCarrier(TRACK_SYNC_STATUS_LIST, carrier);
        List<OrderTransportPO> orderTransportList = Lists.partition(ids, queryOrderPartitionSize).stream()
            .map(orderTransportMapper::selectAllByIds)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());

        List<List<OrderTransportPO>> partition = Lists.partition(orderTransportList, taskPartitionSize);
        for (List<OrderTransportPO> orderTransportPOList : partition) {
            analyzeByOrders(carrier, orderTransportPOList, worker);
        }
        log.info("carrier:{}, transport track sync end------", carrier);
    }

    /**
     * 根据orderNums重新解析轨迹
     * @param orderNums 运单id列表
     */
    public void retryByOrders(List<String > orderNums) {
        if (CollectionUtils.isEmpty(orderNums)) {
            return;
        }
        Map<String, List<OrderTransportPO>> collect = orderTransportMapper.queryOrderTransportList(orderNums).stream()
            .collect(Collectors.groupingBy(OrderTransportPO::getCarrier));
        collect.forEach((carrier, orderTransportPOList) -> {
            BaseTransportTrackWorker worker = transportSyncWorkerFactory.getWorker(carrier);
            analyzeByOrders(carrier, orderTransportPOList, worker);
        });

    }

    private void analyzeByOrders(String carrier, List<OrderTransportPO> orderTransportPOList, BaseTransportTrackWorker worker) {
        List<String> orderNumbers = orderTransportPOList.stream()
            .map(OrderTransportPO::getOrderNumber)
            .collect(Collectors.toList());
        log.info("orderNumbers:{}, begin track sync------", JSON.toJSONString(orderNumbers));
        // 每个运单取最新一条解析记录 去日志表查询 取最新的一条
        Map<String, OrderTraceAnalyzeLogPO> analyzeLogPOMap = analyzeLogMapper.selectByOrderNumbersAndCarrier(orderNumbers, carrier).stream()
            .collect(Collectors.toMap(OrderTraceAnalyzeLogPO::getOrderNumber, Function.identity(),
                (v1, v2) -> v1.getId() > v2.getId() ? v1 : v2));
        worker.execute(carrier, orderTransportPOList, orderNumbers, analyzeLogPOMap, queryRemoteRetryTime);
    }

}
