package com.qudian.lme.bifrost.server.config.redis;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * @desc: 过期策略，暂未使用
 * @author: l<PERSON><PERSON><PERSON>@qudian.com
 * @date: Created at 2020-11-06 09:59
 */
@Configuration
@ConfigurationProperties(prefix = "expire.cache")
public class ExpireCache {
    private final Map<String, Duration> initCaches = new HashMap();

    public ExpireCache() {
    }

    public Map<String, Duration> getInitCaches() {
        return this.initCaches;
    }
}
