package com.qudian.lme.bifrost.server.log;

import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.common.enums.RequestLogStatusEnum;
import com.qudian.lme.bifrost.common.log.BizRequestLogParam;
import com.qudian.lme.bifrost.common.log.RequestLogHandle;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.RequestLogMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.RequestLogPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * {@inheritDoc} {@link RequestLogHandle} impl
 *
 * <AUTHOR>
 * @since 2023/8/1
 **/
@Slf4j
@Component
public class RequestLogHandleImpl implements RequestLogHandle<RequestLogPO> {

    @Resource
    private RequestLogMapper mapper;

    @Override
    public RequestLogPO transfer(String traceId, String url, String requestBody, String responseBody, boolean success, BizRequestLogParam param) {
        return RequestLogPO.builder()
            .status(success ? RequestLogStatusEnum.SUCCESS.getValue() : RequestLogStatusEnum.FAIL.getValue())
            .traceId(traceId)
            .requestUrl(url)
            .carrier(param.getCarrier())
            .forwarderType(param.getForwarderType())
            .orderNumbers(JSON.toJSONString(param.getOrderNumbers()))
            .requestBody(requestBody)
            .response(responseBody)
            .build();
    }

    @Override
    public void saveLog(RequestLogPO log) {
        mapper.insert(log);
    }
}
