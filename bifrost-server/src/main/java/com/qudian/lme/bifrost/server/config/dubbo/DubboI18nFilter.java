package com.qudian.lme.bifrost.server.config.dubbo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * Dubbo国际化，调用前置过滤器
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/15
 **/
@Slf4j
@Activate(group = CommonConstants.PROVIDER)
public class DubboI18nFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // before filter ...
        //国际化参数
        this.beforeFilter();
        Result result = invoker.invoke(invocation);
        // after filter ...
        return result;
    }

    private void beforeFilter() {
        String lang = RpcContext.getContext().getAttachment("lang");   //en-US
        Locale locale = new Locale(Locale.US.getLanguage(), Locale.US.getCountry());
        log.info("[Locale_filter] current locale:{} ", LocaleContextHolder.getLocale());
        if(StringUtils.isBlank(lang) || !lang.contains("-")) {
            LocaleContextHolder.setLocale(locale);
            return;
        }
        log.info("[dubbo_i18n_filter] before filter lang:{}", lang);
        try {
            String[] langCountry = lang.split("-");
            locale = new Locale(langCountry[0], langCountry[1]);
        } catch (Exception e) {
            log.warn("[dubbo_i18n_filter] lang convert to locale.", e);
            //nothing to do. passing by logging
        }
        log.info("[Locale_filter] now locale:{} ", locale);
        LocaleContextHolder.setLocale(locale);
    }
}
