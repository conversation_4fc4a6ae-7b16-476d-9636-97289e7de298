package com.qudian.lme.bifrost.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.convertor.Track51TransportTrackStruct;
import com.qudian.lme.bifrost.infrastructure.repository.remote.Track51RemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.track51.Track51AusPostResponseDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.track51.Track51TransportRequestVO;
import com.qudian.lme.bifrost.server.StartTestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/16
 **/
@Slf4j
public class Track51RemoteServiceTest extends StartTestApplication {

    @Resource
    private Track51RemoteService track51RemoteService;
    @Resource
    private Track51TransportTrackStruct track51TransportTrackStruct;


    @Test
    public void test() {

        Track51TransportRequestVO requestVO = new Track51TransportRequestVO();
        requestVO.setTransportNos(Lists.newArrayList("RCM471743301000935009"));

        List<Track51AusPostResponseDTO> ausPostData = track51RemoteService.getAusPostData(requestVO, null);

        log.warn("resp:{}", JSON.toJSONString(ausPostData));
    }
    @Test
    public void test222() {

//        Track51TransportRequestVO requestVO = new Track51TransportRequestVO();
//        requestVO.setTransportNos(Lists.newArrayList("RCM471743301000935009"));
//        ExpressDeliveryOrderInfo expressDeliveryOrderInfo = new ExpressDeliveryOrderInfo().setTransportLabelNo("930739982").setCourierCode("gls-es").setTrackingPostalCode("28011");
//         track51RemoteService.subscribeTrace(Collections.singletonList(expressDeliveryOrderInfo));
    }

    @Test
    public void test_convert() {
        Track51TransportRequestVO requestVO = new Track51TransportRequestVO();
        requestVO.setTransportNos(Lists.newArrayList("RCM471743301000935009"));

        List<Track51AusPostResponseDTO> ausPostData = track51RemoteService.getAusPostData(requestVO, null);
        List<TransportTrackRespVO.TrackingResult> collect = ausPostData.stream()
            .map(v-> track51TransportTrackStruct.toVOResult(v, ImmutableMap.of("RCM471743301000935009", "test1017-1238")))
            .collect(Collectors.toList());
        log.warn("resp:{}", JSON.toJSONString(collect));
    }
}
