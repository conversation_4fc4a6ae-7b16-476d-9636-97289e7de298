package com.qudian.lme.bifrost.server.service.trace;

import com.qudian.lme.bifrost.application.factory.sfExpress.trace.SfExpressTracePipeline;
import com.qudian.lme.bifrost.server.StartTestApplication;

import javax.annotation.Resource;

/**
 * <p>文件名称:com.qudian.lme.bifrost.server.service.trace.SfExpressTracePipelineTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
public class SfExpressTracePipelineTest extends StartTestApplication {
    @Resource
    private SfExpressTracePipeline sfExpressTracePipeline;

    
}
