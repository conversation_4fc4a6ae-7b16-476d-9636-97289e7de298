package com.qudian.lme.bifrost.server;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>文件名称:com.qudian.lme.driver.server.StartApplicationTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/27
 */
@RunWith(SpringRunner.class)
@Rollback
@Transactional
@ActiveProfiles("local")
@SpringBootTest(classes = {StartServerApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@TypeExcludeFilters({TestExcludeFilter.class})
public class StartTestApplication {
}
