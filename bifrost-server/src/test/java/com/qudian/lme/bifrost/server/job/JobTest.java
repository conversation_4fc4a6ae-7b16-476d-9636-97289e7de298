package com.qudian.lme.bifrost.server.job;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.facade.OrderTransportFacade;
import com.qudian.lme.bifrost.api.vo.request.CancelTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.PushSingleTransportReqVO;
import com.qudian.lme.bifrost.api.vo.request.SingleOrderTransportReqVO;
import com.qudian.lme.bifrost.api.vo.response.PrintLabelResponseVO;
import com.qudian.lme.bifrost.common.utils.HttpClient;
import com.qudian.lme.bifrost.infrastructure.repository.remote.GlsService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.Envio;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.GrabaServiciosResponse;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.Importes;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.Referencia;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.Referencias;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.Remite;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.Servicios;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.exp.GetExpCli;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.gls.exp.GetExpCliResponseVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.impl.speed.SoapRequestGenerator;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import com.qudian.lme.bifrost.server.StartTestApplication;
import com.sun.xml.bind.marshaller.NamespacePrefixMapper;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;
import org.w3c.dom.CDATASection;

import javax.annotation.Resource;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.PropertyException;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPConstants;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;
import javax.xml.stream.XMLStreamException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/8/24
 **/
public class JobTest extends StartTestApplication {

    @Resource
    private TransportTraceSyncJob transportTraceSyncJob;
    @Resource
    private SoapRequestGenerator soapRequestGenerator;
    @Resource
    private GlsService glsService;
    @Resource
    private OrderTransportFacade facade;

    @Rollback(value = false)
    @Test
    public void test() {
         String order = "EX17143549921101";
         transportTraceSyncJob.retryByOrders(Lists.newArrayList(order));

//       transportTraceSyncJob.processByCarrier(CarrierTypeEnum.AUS_POST.getName());

//         transportTraceSyncJob.process(null);

    }
//    @Test
//    public void test2(){
//        String s = "2024-01-06 01:25:06";
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
//    }
    @Test
    public void  test2(){
        ArrayList<OrderTransportPO> strings = new ArrayList<>();
        strings.add(new OrderTransportPO().setState("a"));
        strings.add(new OrderTransportPO().setState("a"));
        Map<Optional<String>, List<OrderTransportPO>> byState = strings.stream()
                .collect(Collectors.groupingBy(v -> Optional.ofNullable(v.getState())));
        System.out.println(byState);
    }
    @Resource
    private HttpClient client;
    
    @Test
    public void test3() throws JAXBException, XMLStreamException {
//        String xmlString = getString();
//        System.out.println(xmlString);
//        String url = "https://wsclientes.asmred.com/b2b.asmx?wsdl";
//        String s = client.postXmlData(url, xmlString, null);
    }

    @NotNull
    private static String getString() throws JAXBException {
        Servicios request = new Servicios();
        request.setUidcliente("6BAB7A53-3B6D-4D5A-9450-702D2FAC0B11");

        Envio envio = new Envio();
        envio.setCodbarras("");
        envio.setFecha("14/08/2014");
        envio.setPortes("P");
        envio.setServicio("96");
        envio.setHorario("18");
        envio.setBultos(1);
        envio.setPeso(4);
        envio.setRetorno(0);
        envio.setPod("N");

        Remite remite = new Remite();
        // 设置 Remite 的各个字段
        envio.setRemite(remite);

//        Destinatario destinatario = new Destinatario();
//        // 设置 Destinatario 的各个字段
//        envio.setDestinatario(destinatario);

        Referencias referencias = new Referencias();
        Referencia referencia = new Referencia();
        referencia.setTipo("C");
//        referencias.setReferencia(referencia);
//        referencia.setValue("111122222");
//        referencias.setReferencia(referencia);
//        envio.setReferencias(referencias);

        Importes importes = new Importes();
        importes.setReembolso("10,50");
        envio.setImportes(importes);

        request.setEnvio(envio);
        JAXBContext context = JAXBContext.newInstance(Servicios.class);

        // 创建 marshaller
        Marshaller marshaller = context.createMarshaller();

        // 禁用命名空间
        try {
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
        } catch (PropertyException e) {
            e.printStackTrace();
        }

        // 设置格式化输出和编码
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
        marshaller.setProperty("com.sun.xml.bind.namespacePrefixMapper", new NamespacePrefixMapper() {
            @Override
            public String getPreferredPrefix(String namespaceUri, String suggestion, boolean requirePrefix) {
                // 如果是默认命名空间，则返回空字符串，否则返回建议的前缀
                return "http://www.asmred.com/".equals(namespaceUri) ? "" : suggestion;
            }
        });

        StringWriter writer = new StringWriter();
//        writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");

        // 序列化对象到 StringWriter
        marshaller.marshal(request, writer);

        // 打印生成的 XML 字符串
        String string = writer.toString();
        String xmlString = writer.toString();
        xmlString = xmlString.replaceAll("ns2:", "");
        xmlString = xmlString.replaceAll(":ns2", "");
        return xmlString;
    }

    @Test
    public void test333() throws SOAPException, IOException, TransformerException, JAXBException {
        // 创建SOAP消息工厂
        MessageFactory factory = MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL);
        SOAPMessage message = factory.createMessage();

        // 获取SOAP消息的部分
        SOAPPart soapPart = message.getSOAPPart();

        // 创建SOAP消息的消息体
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.setPrefix("soap12");
        envelope.removeNamespaceDeclaration("env");
        envelope.addNamespaceDeclaration("xsd", "http://www.w3.org/2001/XMLSchema");
        envelope.addNamespaceDeclaration("xsi", "http://www.w3.org/2001/XMLSchema-instance");

        SOAPBody body = envelope.getBody();
        body.setPrefix("soap12");

        // 创建消息体的内容
        SOAPElement bodyElement = body.addChildElement("GrabaServicios", "", "http://www.asmred.com/");

        String string = getString();
        CDATASection cdata = soapPart.createCDATASection(string);
//        System.out.println(string);
//        byte[] stringBytes = string.getBytes(StandardCharsets.UTF_8);
//        String encodedString = new String(stringBytes, StandardCharsets.UTF_8);
        bodyElement.addChildElement("docIn").appendChild(cdata);
        // 将SOAP消息转换为字符串，并添加XML声明
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
        transformer.setOutputProperty(OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(message.getSOAPPart()), new StreamResult(writer));
        String xmlString = writer.toString();
        byte[] stringBytes = xmlString.getBytes(StandardCharsets.UTF_8);
        String encodedString = new String(stringBytes, StandardCharsets.UTF_8);
        System.out.println(encodedString);
//
//        // 打印SOAP消息
//        System.out.println("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
//        System.out.println(xmlString);
        // 打印SOAP消息
//       String a = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
//               "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
//               "  <soap:Body>\n" +
//               "    <GrabaServicios xmlns=\"http://www.asmred.com/\">\n" +
//               "      <docIn>\n" +
//               "        <Servicios uidcliente=\"6BAB7A53-3B6D-4D5A-9450-702D2FAC0B11\" xmlns=\"http://www.asmred.com/\">\n" +
//               "  <Envio codbarras=\"\">\n" +
//               "    <Fecha>14/08/2014</Fecha>\n" +
//               "    <Portes>P</Portes>\n" +
//               "    <Servicio>96</Servicio>\n" +
//               "    <Horario>18</Horario>\n" +
//               "    <Bultos>1</Bultos>\n" +
//               "    <Peso>4</Peso>\n" +
//               "    <Retorno>0</Retorno>\n" +
//               "    <Pod>N</Pod>\n" +
//               "    <Remite>\n" +
//               "      <Plaza></Plaza>\n" +
//               "      <Nombre><![CDATA[ARG Solutions]]></Nombre>\n" +
//               "      <Direccion><![CDATA[C/Dolca de Provenca 2222]]></Direccion>\n" +
//               "      <Poblacion><![CDATA[Premia de Dalt]]></Poblacion>\n" +
//               "      <Provincia><![CDATA[Barcelona]]></Provincia>\n" +
//               "      <Pais>34</Pais>\n" +
//               "      <CP>08338</CP>\n" +
//               "      <Telefono><![CDATA[961111111]]></Telefono>\n" +
//               "      <Movil><![CDATA[]]></Movil>\n" +
//               "      <Email><![CDATA[]]></Email>\n" +
//               "      <Observaciones><![CDATA[]]></Observaciones>\n" +
//               "    </Remite>\n" +
//               "    <Destinatario>\n" +
//               "      <Codigo></Codigo>\n" +
//               "      <Plaza></Plaza>\n" +
//               "      <Nombre><![CDATA[Mr Test]]></Nombre>\n" +
//               "      <Direccion><![CDATA[Calle Juan Esplandi 513b]]></Direccion>\n" +
//               "      <Poblacion><![CDATA[Madrid]]></Poblacion>\n" +
//               "      <Provincia><![CDATA[Madrid]]></Provincia>\n" +
//               "      <Pais>34</Pais>\n" +
//               "      <CP>28007</CP>\n" +
//               "      <Telefono><![CDATA[933888777]]></Telefono>\n" +
//               "      <Movil><![CDATA[666777888]]></Movil>\n" +
//               "      <Email><![CDATA[<EMAIL>]]></Email>\n" +
//               "      <Observaciones><![CDATA[Fragile]]></Observaciones>\n" +
//               "      <ATT><![CDATA[Mr Test Contact]]></ATT>\n" +
//               "    </Destinatario>\n" +
//               "    <Referencias> <!-- remember put a different value in each test -->\n" +
//               "      <Referencia tipo=\"C\"><![CDATA[111122222]]></Referencia>\n" +
//               "    </Referencias>\n" +
//               "    <Importes>\n" +
//               "      <Reembolso>10,50</Reembolso>\n" +
//               "    </Importes>\n" +
//               "  </Envio>\n" +
//               "</Servicios>\n" +
//               "      </docIn>\n" +
//               "    </GrabaServicios>\n" +
//               "  </soap:Body>\n" +
//               "</soap:Envelope>";
        String url = "https://wsclientes.asmred.com/b2b.asmx?wsdl";
        String s = client.postXmlData(url, xmlString, null);
    }

    @SneakyThrows
    @Test
    public void test_getExp() {
        GetExpCli reqDTO = new GetExpCli().setUid("6BAB7A53-3B6D-4D5A-9450-702D2FAC0B11")
            .setCodigo("61771176196487");
        String getExp = soapRequestGenerator.generateV2GLsSoapRequest(reqDTO, GetExpCli.class);
        System.out.println(getExp);
        String s = client.postSoapV2("https://wsclientes.asmred.com/b2b.asmx?wsdl", getExp, null);
        System.out.println(s);
        GetExpCliResponseVO soapResponse = (GetExpCliResponseVO) soapRequestGenerator.parseSoapResponse(s, GetExpCliResponseVO.class);
        System.out.println(JSON.toJSONString(soapResponse));
    }
    
    @Test
    public void testResponse(){
        // String s = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
        //         "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
        //         "    <soap:Body>\n" +
        //         "        <GrabaServiciosResponse xmlns=\"http://www.asmred.com/\">\n" +
        //         "            <GrabaServiciosResult>\n" +
        //         "                <Servicios xmlns=\"\">\n" +
        //         "                    <Envio codbarras=\"61771175851213\" uid=\"3bfd705f-9943-4659-b278-711992c568b6\" codexp=\"948055591\">\n" +
        //         "                        <Resultado return=\"0\" />\n" +
        //         "                        <Errores />\n" +
        //         "                        <Referencias>\n" +
        //         "                            <Referencia tipo=\"0\">111122222</Referencia>\n" +
        //         "                            <Referencia tipo=\"C\">111122222</Referencia>\n" +
        //         "                        </Referencias>\n" +
        //         "                    </Envio>\n" +
        //         "                </Servicios>\n" +
        //         "            </GrabaServiciosResult>\n" +
        //         "        </GrabaServiciosResponse>\n" +
        //         "    </soap:Body>\n" +
        //         "</soap:Envelope>";
        String s= "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
            "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
            "    <soap:Body>\n" +
            "        <GetExpCliResponse xmlns=\"http://www.asmred.com/\">\n" +
            "            <GetExpCliResult>\n" +
            "                <expediciones xmlns=\"\">     \n" +
            "                    <exp>\n" +
            "                        <expedicion>771-821898680</expedicion>\n" +
            "                        <albaran>1234561002</albaran>\n" +
            "                        <fecha>25/05/2023 0:00:00</fecha>\n" +
            "                        <FPEntrega>26/05/2023 0:00:00</FPEntrega>\n" +
            "                        <servicio>DISTRIBUCION PROPIA</servicio>\n" +
            "                        <horario>Express19:00</horario>\n" +
            "                        <tipo_portes>P</tipo_portes>\n" +
            "                        <codServicio>14</codServicio>\n" +
            "                        <codHorario>3</codHorario>\n" +
            "                        <nif_org></nif_org>\n" +
            "                        <nombre_org>from name</nombre_org>\n" +
            "                        <calle_org>from address</calle_org>\n" +
            "                        <localidad_org>from city</localidad_org>\n" +
            "                        <cp_org>08100</cp_org>\n" +
            "                        <tfno_org></tfno_org>\n" +
            "                        <departamento_org></departamento_org>\n" +
            "                        <codpais_org>34</codpais_org>\n" +
            "                        <nif_dst>11223344F</nif_dst>\n" +
            "                        <nombre_dst>consignee name</nombre_dst>\n" +
            "                        <calle_dst>consignee address</calle_dst>\n" +
            "                        <localidad_dst>consignee city</localidad_dst>\n" +
            "                        <cp_dst>28004</cp_dst>\n" +
            "                        <tfno_dst>*********</tfno_dst>\n" +
            "                        <departamento_dst></departamento_dst>\n" +
            "                        <codpais_dst>34</codpais_dst>\n" +
            "                        <codplaza_cli>771</codplaza_cli>\n" +
            "                        <codcli>601</codcli>\n" +
            "                        <nmCliente>Pruebas WS                        </nmCliente>\n" +
            "                        <codcli_may></codcli_may>\n" +
            "                        <codcli_red_pag>3449</codcli_red_pag>\n" +
            "                        <nmClired>PRUEBAS                           </nmClired>\n" +
            "                        <codprov_red_org>18000</codprov_red_org>\n" +
            "                        <ProvOrg>PRUEBAS                           </ProvOrg>\n" +
            "                        <codprov_red_dst>18000</codprov_red_dst>\n" +
            "                        <ProvDst>PRUEBAS                           </ProvDst>\n" +
            "                        <codplaza_prv></codplaza_prv>\n" +
            "                        <codprov></codprov>\n" +
            "                        <bultos>0</bultos>\n" +
            "                        <kgs>0,0</kgs>\n" +
            "                        <vol>0,000</vol>\n" +
            "                        <kgsvol_prv_red_org></kgsvol_prv_red_org>\n" +
            "                        <kgsvol_prv_red_dst></kgsvol_prv_red_dst>\n" +
            "                        <kgsvol_cli>0,0</kgsvol_cli>\n" +
            "                        <kgsvol_prv></kgsvol_prv>\n" +
            "                        <hoja></hoja>\n" +
            "                        <codrepartidor></codrepartidor>\n" +
            "                        <repartidor></repartidor>\n" +
            "                        <fHoja></fHoja>\n" +
            "                        <estHoja>-1</estHoja>\n" +
            "                        <punteados></punteados>\n" +
            "                        <reasignados></reasignados>\n" +
            "                        <fPunteo></fPunteo>\n" +
            "                        <plzorg>XXX</plzorg>\n" +
            "                        <plazaorg>PRUEBAS</plazaorg>\n" +
            "                        <plzdst>XXX</plzdst>\n" +
            "                        <plazadst>PRUEBAS</plazadst>\n" +
            "                        <plzpag>XXX</plzpag>\n" +
            "                        <plazapag>PRUEBAS</plazapag>\n" +
            "                        <codplaza_org>771</codplaza_org>\n" +
            "                        <codplaza_dst>771</codplaza_dst>\n" +
            "                        <codplaza_pag>771</codplaza_pag>\n" +
            "                        <borrado>S</borrado>\n" +
            "                        <codestado>5</codestado>\n" +
            "                        <estado>ANULADA</estado>\n" +
            "                        <pod></pod>\n" +
            "                        <tipopod></tipopod>\n" +
            "                        <nmtipopod></nmtipopod>\n" +
            "                        <firma></firma>\n" +
            "                        <DniEntrega></DniEntrega>\n" +
            "                        <NombreEntrega></NombreEntrega>\n" +
            "                        <digCaja></digCaja>\n" +
            "                        <digLote></digLote>\n" +
            "                        <digPosicion></digPosicion>\n" +
            "                        <digTipo></digTipo>\n" +
            "                        <incidencia>SIN INCIDENCIA</incidencia>\n" +
            "                        <Observacion>transport notes</Observacion>\n" +
            "                        <codplaza_vlj></codplaza_vlj>\n" +
            "                        <codvalija></codvalija>\n" +
            "                        <ida_vuelta></ida_vuelta>\n" +
            "                        <codsaca></codsaca>\n" +
            "                        <columna>4</columna>\n" +
            "                        <calidad>-1</calidad>\n" +
            "                        <codexp>821898680</codexp>\n" +
            "                        <nuo>821898680</nuo>\n" +
            "                        <codbar>61771137005167</codbar>\n" +
            "                        <uidExp>8f55421b-8965-408e-8eac-ae8a18556406</uidExp>\n" +
            "                        <solCli>0</solCli>\n" +
            "                        <TipoMercancia></TipoMercancia>\n" +
            "                        <DescMercancia></DescMercancia>\n" +
            "                        <dac>SIN RCS</dac>\n" +
            "                        <retorno>SIN RETORNO</retorno>\n" +
            "                        <refInternacional></refInternacional>\n" +
            "                        <refGlsG></refGlsG>\n" +
            "                        <refGlsN></refGlsN>\n" +
            "                        <refZ></refZ>\n" +
            "                        <Reembolso>0,0000</Reembolso>\n" +
            "                        <email_dst><EMAIL></email_dst>\n" +
            "                        <detallebultos />\n" +
            "                        <referenciasxbulto>\n" +
            "                            <referencia>\n" +
            "                                <bulto>0</bulto>\n" +
            "                                <tipo>C</tipo>\n" +
            "                                <referencia>1234561002</referencia>\n" +
            "                            </referencia>\n" +
            "                        </referenciasxbulto>\n" +
            "                        <tracking_list>\n" +
            "                            <tracking>\n" +
            "                                <fecha>25/05/2023 15:31:50</fecha>\n" +
            "                                <tipo>ESTADO</tipo>\n" +
            "                                <plaza>-1</plaza>\n" +
            "                                <evento>GRABADO </evento>\n" +
            "                                <prioridad>1</prioridad>\n" +
            "                                <codigo>-10</codigo>\n" +
            "                                <nombreplaza>OFICINA CENTRAL</nombreplaza>\n" +
            "                            </tracking>\n" +
            "                            <tracking>\n" +
            "                                <fecha>01/06/2023 13:01:40</fecha>\n" +
            "                                <tipo>ESTADO</tipo>\n" +
            "                                <plaza>-1</plaza>\n" +
            "                                <evento>ANULADA </evento>\n" +
            "                                <prioridad>1</prioridad>\n" +
            "                                <codigo>5</codigo>\n" +
            "                                <nombreplaza>OFICINA CENTRAL</nombreplaza>\n" +
            "                            </tracking>\n" +
            "                        </tracking_list>\n" +
            "                        <digitalizaciones />\n" +
            "                    </exp>\n" +
            "                </expediciones>\n" +
            "            </GetExpCliResult>\n" +
            "        </GetExpCliResponse>\n" +
            "    </soap:Body>\n" +
            "</soap:Envelope>";
       String o = client.parserResponseBody(s);
       System.out.println(o);
        
    }
    
    @Test
    public void test222(){
        PushSingleTransportReqVO pushSingleTransportReqVO = new PushSingleTransportReqVO();
        PushSingleTransportReqVO.Shipment shipment = new PushSingleTransportReqVO.Shipment();
        PushSingleTransportReqVO.SenderInfo senderInfo = new PushSingleTransportReqVO.SenderInfo();
        senderInfo.setSender("test");
        senderInfo.setSenderAddress("C/Dolca de Provenca 2222");
        senderInfo.setSenderCity("Premia de Dalt");
        senderInfo.setSenderProvince("Barcelona");
        senderInfo.setSenderPostcode("08338");
        senderInfo.setSenderMobilePhone("961111111");
        PushSingleTransportReqVO.RecipientInfo recipientInfo = new PushSingleTransportReqVO.RecipientInfo();
        recipientInfo.setRecipient("test222");
        recipientInfo.setRecipientAddress("Calle Juan Esplandi 513b");
        recipientInfo.setReceiptCity("Madrid");
        recipientInfo.setReceiptPostcode("28007");
        recipientInfo.setReceiptProvince("Madrid");
        recipientInfo.setRecipientMobilePhone("933888777");
        shipment.setOrderNumber("111122222222");
        shipment.setSenderInfo(senderInfo);
        shipment.setRecipientInfo(recipientInfo);
        PushSingleTransportReqVO.ItemInfo itemInfo = new PushSingleTransportReqVO.ItemInfo();
        itemInfo.setWeight(12D);
        shipment.setItemInfo(Collections.singletonList(itemInfo));
        pushSingleTransportReqVO.setShipment(shipment);
        glsService.createOrder(pushSingleTransportReqVO);
    }
    
    @Test
    public void test8w88(){
        CancelTransportReqVO cancelTransportReqVO = new CancelTransportReqVO();
        cancelTransportReqVO.setOrderNumber("111122222222");
        glsService.cancelTransport(cancelTransportReqVO);
    }
    
    @Test
    public void test677(){
        String s  = "<GrabaServiciosResponse xmlns=\"http://www.asmred.com/\">\n" +
                "<GrabaServiciosResult>\n" +
                "    <Servicios xmlns=\"\">\n" +
                "        <Envio codbarras=\"61771176077269\" codexp=\"948753679\" uid=\"e6dbb639-a9c5-48ca-9df3-0596a5fa5d26\"><Resultado return=\"0\"/>\n" +
                "        <Errores/>\n" +
                "        <Referencias>\n" +
                "            <Referencia tipo=\"0\">97254</Referencia>\n" +
                "        </Referencias>\n" +
                "        </Envio>\n" +
                "        </Servicios>\n" +
                "        </GrabaServiciosResult>\n" +
                "        </GrabaServiciosResponse>";
        GrabaServiciosResponse o = (GrabaServiciosResponse)soapRequestGenerator.parseSoapResponse(s, GrabaServiciosResponse.class);
        System.out.println(o);
    }
    
    @Test
    public void test888(){
        SingleOrderTransportReqVO cancelTransportReqVO = new SingleOrderTransportReqVO();
        cancelTransportReqVO.setOrderNumber("abc1221");
        PrintLabelResponseVO printLabelResponseVO = glsService.printSingleLabel(cancelTransportReqVO);
//        System.out.println(printLabelResponseVO);
//        String s = "<EtiquetaEnvioV2Response xmlns=\"http://www.asmred.com/\"><EtiquetaEnvioV2Result><Etiquetas xmlns=\"\"><Etiqueta>Tg0KcTcwMA0KTE8xOTAsMTAwLDUwMCwzDQpMTzI2MCwyNTQsNDUwLDMNCkxPMjYwLDM0Miw0NTAsMw0KTE8yNjAsNDMyLDQ1MCwzDQpMTzI2MCw4MDAsNDUwLDMNCkEyNDUsNDQwLDMsMSwxLDEsTiwiT0JTRVJWQUNJLiINCkIxLDgwMCwzLDJDLDQsOCwyMDAsQiwiNjE3NzExNzYxNzQ0MTcwMDEiDQpBMDA1LDIxNSwwLDMsMSwyLE4sIjExLzA0LzIwMjQiDQpBMTkwLDExMCwwLDMsMSwyLE4sInRlc3QyMjIiDQpBMTkwLDE0NSwwLDMsMSwyLE4sIkNhbGxlIEp1YW4gRXNwbGFuZGkgNTEzYiINCkExOTAsMTgwLDAsMywxLDIsTiwiTWFkcmlkIg0KQTE5MCwyMTUsMCwzLDEsMixOLCJNQURSSUQiDQpBNTMwLDIxNSwwLDMsMSwyLE4sIiINCkEyNjAsMjYwLDAsMSwxLDEsTiwidGVzdCINCkEyNjAsMjc1LDAsMSwxLDEsTiwiQy9Eb2xjYSBkZSBQcm92ZW5jYSAyMjIyIg0KQTI2MCwyOTAsMCwxLDEsMSxOLCJQcmVtaWEgZGUgRGFsdCINCkEyNjAsMzA1LDAsMSwxLDEsTiwiTUFEUklEIg0KQTI2MCwzMjAsMCwxLDEsMSxOLCIiDQpBNTMwLDMyMCwwLDEsMSwxLE4sIjM0NDkoNzcxLzYwMSkiDQpBMjYwLDM1MCwwLDIsMSwxLE4sIiINCkEyNjAsNDQwLDAsNCwxLDIsTiwiQnVzaW5lc3NQYXJjZWwgKDI0KSAiDQpBMjYwLDQ5MCwwLDQsMSwyLE4sIiAiDQpBNDYwLDQ0MCwwLDQsMSwyLE4sIiINCkE0NjAsNDkwLDAsNCwxLDIsTiwiIg0KQTMwMCw1NDAsMCw1LDMsMixOLCJYWFgiDQpBMjYwLDY1MCwwLDMsMiw0LE4sIlBSVUVCQVMiDQpBMjYwLDcyMCwwLDMsMiw0LE4sIjI4MDA3Ig0KQTQ2MCw3MjAsMCwzLDIsNCxOLCIxLzEiDQpBMDA1LDg0NSwwLDIsMSwxLE4sIkFsYi5DbGkuIg0KQTEwMCw4NDUsMCwyLDMsMSxOLCIxMTExMjIyMjIyMjIiDQpBMDA1LDg3MCwwLDIsMSwxLE4sIlJlZi5DbGkuIg0KQTEwMCw4NzAsMCwyLDMsMSxOLCIxMTExMjIyMjIyMjIiDQpCMSw5NTAsMCw5LDMsNiwxMDAsQiwiMTExMTIyMjIyMjIyIg0KQTE5MCwxLDAsNSwxLDIsTiwiNDM3NzE5NDkxMzkyNzkiDQpBMTc1LDI0MCwzLDEsMSwxLE4sIkRFU1RJTkFUQVJJTyINCkEyNDUsMzM1LDMsMSwxLDEsTiwiUkVNSVRFTlRFIg0KQTAwNSwxODUsMCw0LDMsMyxOLCIzIg0KUDENCkEzMCwxMTcwLDAsMSwxLDEsTiwid3d3Lmdscy1zcGFpbi5lcy9lcy9wb2xpdGljYS1kZS1wcml2YWNpZGFkLyINCg==</Etiqueta></Etiquetas></EtiquetaEnvioV2Result></EtiquetaEnvioV2Response>";
    }


    @Test
    @Rollback(value = false)
    public void test111123() {
        PushSingleTransportReqVO pushSingleTransportReqVO = JSON.parseObject("{\"carrier\":\"CTT\",\"channel\":\"\",\"origin\":\"YourBox\",\"shipment\":{\"itemInfo\":[{\"height\":0.0,\"length\":0.0,\"weight\":0.0,\"width\":0.0}],\"orderNumber\":\"EX17142976651001\",\"recipientInfo\":{\"county\":\"\",\"receiptCountry\":\"ES\",\"receiptPostcode\":\"31001\",\"receiptProvince\":\"Pamplona\",\"recipient\":\"zhangsan\",\"recipientAddress\":\"E.N 125 VALE DE EGUAS CARPINTARIA MIHAI, \",\"recipientMobilePhone\":\"0611231231\"},\"returneeInfo\":{\"county\":\"Keysborough\",\"returnee\":\"刘云鹏\",\"returneeAddress\":\" , Melbourne ,Australia\",\"returneeCountry\":\"AU\",\"returneeMobilePhone\":\"\",\"returneePostcode\":\"3068\",\"returneeProvince\":\"Mel\"},\"senderInfo\":{\"county\":\"Keysborough\",\"sender\":\"刘云鹏\",\"senderAddress\":\" , Melbourne ,Australia\",\"senderCountry\":\"AU\",\"senderMobilePhone\":\"\",\"senderPostcode\":\"3068\",\"senderProvince\":\"Mel\"}},\"warehouseLocationState\":\"Mel\"}", PushSingleTransportReqVO.class);

        facade.pushSingleTransport(pushSingleTransportReqVO);
    }



}
