package com.qudian.lme.bifrost.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.infrastructure.repository.remote.UPSRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.UPSTransportOrderTraceReqDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ups.UPSTransportOrderTraceRespDTO;
import com.qudian.lme.bifrost.server.StartTestApplication;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/6
 **/
public class UPSRemoteServiceTest extends StartTestApplication {

    @Resource
    private UPSRemoteService upsRemoteService;

    @Test
    public void test() {
        UPSTransportOrderTraceReqDTO reqDTO = new UPSTransportOrderTraceReqDTO();
        reqDTO.setTrackIdList(Lists.newArrayList("9214490328168916895670", "9214490328168916891696", "12331"));

        List<UPSTransportOrderTraceRespDTO> respDTOList = upsRemoteService.packageTrack(reqDTO, Lists.newArrayList("123"));

        System.out.println("--------" + JSON.toJSONString(respDTOList));
    }

}
