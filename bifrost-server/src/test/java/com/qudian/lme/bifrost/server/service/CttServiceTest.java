package com.qudian.lme.bifrost.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.CTTService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.ShippingStatusReqDTO;
import com.qudian.lme.bifrost.server.StartTestApplication;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @Author: yangxinye
 * @Date: 2024/4/25
 * @Version: 1.0.0
 **/
public class CttServiceTest extends StartTestApplication {

    @Resource
    private CTTService cttService;



    @Test
    public void test1() {
        ShippingStatusReqDTO dto =new ShippingStatusReqDTO();
        dto.setOrderInfoList(Lists.newArrayList(new ShippingStatusReqDTO.ShippingOrderInfo().setOrderId("111").setTrackingId("0082800082809800348645")));
        TransportTrackRespVO transportTrackRespVO = cttService.shippingStatus(dto);
        System.out.println(JSON.toJSONString(transportTrackRespVO));
    }


}
