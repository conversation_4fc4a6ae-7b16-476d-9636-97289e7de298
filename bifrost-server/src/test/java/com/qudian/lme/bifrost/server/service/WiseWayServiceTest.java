package com.qudian.lme.bifrost.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.convertor.WiseWayTransportTrackStruct;
import com.qudian.lme.bifrost.infrastructure.repository.remote.WiseWayService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackReqVO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackRespVO;
import com.qudian.lme.bifrost.server.StartTestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/10/17
 **/
@Slf4j
public class WiseWayServiceTest extends StartTestApplication {

    @Resource
    private WiseWayService wiseWayService;

    @Resource
    private WiseWayTransportTrackStruct wiseWayTransportTrackStruct;

    @Test
    public void test_track() {

        WiseWayOrderTrackReqVO reqVO = new WiseWayOrderTrackReqVO();
        reqVO.setTrackNums(Lists.newArrayList("36CUC500000301000935008"));

        List<WiseWayOrderTrackRespVO> wiseWayOrderTrackRespVOS = wiseWayService.queryOrderTrack(reqVO);
        log.warn("resp:{}", JSON.toJSONString(wiseWayOrderTrackRespVOS));
    }

    @Test
    public void test_track_convert() {

        WiseWayOrderTrackReqVO reqVO = new WiseWayOrderTrackReqVO();
        reqVO.setTrackNums(Lists.newArrayList("36CUC500000301000935008"));

        Map<String, String> map = ImmutableMap.of("test-13", "36CUC500000301000935008");

        List<WiseWayOrderTrackRespVO> wiseWayOrderTrackRespVOS = wiseWayService.queryOrderTrack(reqVO);
        List<TransportTrackRespVO.TrackingResult> collect = wiseWayOrderTrackRespVOS.stream().map(v -> wiseWayTransportTrackStruct.toTrackResult(v, map)).collect(Collectors.toList());
        log.warn("resp:{}", JSON.toJSONString(collect));
    }
}
