package com.qudian.lme.bifrost.server.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.qudian.lme.bifrost.api.enums.CarrierTypeEnum;
import com.qudian.lme.bifrost.api.enums.ForwarderTypeEnum;
import com.qudian.lme.bifrost.api.vo.response.TransportTrackRespVO;
import com.qudian.lme.bifrost.application.convertor.EweTransportTrackStruct;
import com.qudian.lme.bifrost.application.convertor.WiseWayTransportTrackStruct;
import com.qudian.lme.bifrost.application.factory.transportTrack.TransportTrackWorkerFactory;
import com.qudian.lme.bifrost.application.factory.transportTrack.worker.EweTransportTrackWorker;
import com.qudian.lme.bifrost.application.factory.transportTrack.worker.WisewayTransportTrackWorker;
import com.qudian.lme.bifrost.application.mq.dto.TransportDeliveredMqDTO;
import com.qudian.lme.bifrost.application.mq.dto.TransportTrackChangeMqDTO;
import com.qudian.lme.bifrost.common.enums.MqTagEnum;
import com.qudian.lme.bifrost.infrastructure.repository.remote.EweRemoteService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.WiseWayService;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.EweShippingStatusRespDTO;
import com.qudian.lme.bifrost.infrastructure.repository.remote.dto.wiseWay.WiseWayOrderTrackRespVO;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTraceAnalyzeLogMapper;
import com.qudian.lme.bifrost.infrastructure.repository.database.mapper.OrderTransportMapper;
import com.qudian.lme.bifrost.infrastructure.repository.mq.MqProducer;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTraceAnalyzeLogPO;
import com.qudian.lme.bifrost.infrastructure.repository.database.po.OrderTransportPO;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.qudian.lme.bifrost.common.enums.TransportStatusEnum.DISPATCH_SHIPMENTS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * {@inheritDoc} {@link TransportTraceSyncJob} test
 *
 * <AUTHOR>
 * @since 2023/8/2
 **/
@RunWith(MockitoJUnitRunner.class)
public class TransportTraceSyncJobTest {

    private static final String ARTICLE_ID_A = "RCM474249401000935006";
    private static final String ORDER_NUMBER_A = "F00000000162431";
    private static final String ORDER_NUMBER_QUERY_FAIL = "F00000000162432";
    private static final Long ORDER_TRANSPORT_PO_ID = 9999911L;

    private static final Long ORDER_TRANSPORT_PO_ID_QUERY_FAIL = 9999923L;

    private static final String LAST_ANALYZE_TIME = "2023-08-02 00:00:00";

    @Mock
    private MqProducer mqProducer;
    @Mock
    private OrderTransportMapper orderTransportMapper;
    @Mock
    private OrderTraceAnalyzeLogMapper orderTraceAnalyzeLogMapper;
    @Mock
    private EweRemoteService eweRemoteService;
    @Mock
    private WiseWayService wiseWayService;
    @Mock
    private EweTransportTrackStruct eweTransportTrackStruct;
    @Mock
    private WiseWayTransportTrackStruct wiseWayTransportTrackStruct;
    @Mock
    private TransportTrackWorkerFactory transportSyncWorkerFactory;
    @InjectMocks
    private TransportTraceSyncJob transportTraceSyncJob;
    @InjectMocks
    private EweTransportTrackWorker eweTransportSyncWorker;
    @InjectMocks
    private WisewayTransportTrackWorker wisewayTransportTrackWorker;

    @Test
    public void test_ewe_delivered() {
        List<OrderTransportPO> poList = new ArrayList<>();
        OrderTransportPO po1 = OrderTransportPO.builder()
            .id(ORDER_TRANSPORT_PO_ID)
            .articleId(ARTICLE_ID_A)
            .orderNumber(ORDER_NUMBER_A)
            .status(DISPATCH_SHIPMENTS.getCode())
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .forwarderStatus("In transit")
            .deleteFlag(0)
            .createdTime(new Date())
            .updatedTime(new Date())
            .build();
        // 查询失败的运单
        OrderTransportPO po2 = OrderTransportPO.builder()
            .id(ORDER_TRANSPORT_PO_ID_QUERY_FAIL)
            .articleId(ARTICLE_ID_A)
            .orderNumber(ORDER_NUMBER_QUERY_FAIL)
            .status(DISPATCH_SHIPMENTS.getCode())
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .forwarderStatus("In transit")
            .deleteFlag(0)
            .createdTime(new Date())
            .updatedTime(new Date())
            .build();
        poList.add(po1);
        poList.add(po2);

        OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO = new OrderTraceAnalyzeLogPO()
            .setAnalyzeTime(LAST_ANALYZE_TIME)
            .setOrderNumber(ORDER_NUMBER_A)
            .setForwarderType(ForwarderTypeEnum.EWE.getName());
        OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO1 = new OrderTraceAnalyzeLogPO()
            .setAnalyzeTime(LAST_ANALYZE_TIME)
            .setOrderNumber(ORDER_NUMBER_QUERY_FAIL)
            .setForwarderType(ForwarderTypeEnum.EWE.getName());

        String s = "";
        EweShippingStatusRespDTO statusRespDTO = JSON.parseObject(s, EweShippingStatusRespDTO.class);
        String s1 = "{\"trackingResults\":[{\"articleId\":\"RCM473878601000935009\",\"orderNumber\":\"F00000000162431\",\"status\":\"Delivered\",\"events\":[{\"desc\":\"Delivered to post office box\",\"date\":\"2023-08-02 07:03:13\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-01 11:01:49\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-01 11:01:48\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-01 10:04:12\"},{\"desc\":\"In transit to next facility in MULGRAVE VIC\",\"date\":\"2023-08-01 00:19:16\"},{\"location\":\"SUNSHINE WEST VIC\",\"desc\":\"Item processed at facility\",\"date\":\"2023-07-31 21:55:29\"},{\"desc\":\"Received and ready for processing\",\"date\":\"2023-07-31 21:25:29\"},{\"desc\":\"Shipping information approved by Australia Post\",\"date\":\"2023-07-31 02:41:02\"},{\"desc\":\"Shipping information received by Australia Post\",\"date\":\"2023-07-29 14:51:43\"},{\"location\":\"Sydney,NSW\",\"desc\":\"Shipment information received\",\"date\":\"2023-07-28 20:47:36\"}]},{\"articleId\":\"F00000000162432\"}]}";
        TransportTrackRespVO respVO = JSON.parseObject(s1, TransportTrackRespVO.class);

        ReflectionTestUtils.setField(eweTransportSyncWorker, "partitionSize", 10);
        ReflectionTestUtils.setField(eweTransportSyncWorker, "timeSleepMilliseconds", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryOrderPartitionSize", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "taskPartitionSize", 10);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryRemoteRetryTime", 3);
        when(transportSyncWorkerFactory.getWorker(any())).thenReturn(eweTransportSyncWorker);
        when(orderTransportMapper.selectIdByStatusListAndCarrier(any(), any())).thenReturn(Lists.newArrayList(1L, 2L));
        when(orderTransportMapper.selectAllByIds(any())).thenReturn(poList);
        when(orderTraceAnalyzeLogMapper.selectByOrderNumbersAndCarrier(any(), any())).thenReturn(Lists.newArrayList(orderTraceAnalyzeLogPO, orderTraceAnalyzeLogPO1));
        when(eweRemoteService.shippingStatus(any())).thenReturn(statusRespDTO);
        when(eweTransportTrackStruct.toVO(any())).thenReturn(respVO);
        ArgumentCaptor<TransportTrackChangeMqDTO> changeMqCaptor = ArgumentCaptor.forClass(TransportTrackChangeMqDTO.class);
        ArgumentCaptor<TransportDeliveredMqDTO> deliveredMqCaptor = ArgumentCaptor.forClass(TransportDeliveredMqDTO.class);

        transportTraceSyncJob.processByCarrier(CarrierTypeEnum.AUS_POST.getName());

        verify(mqProducer, times(1)).sendEntityMsg(deliveredMqCaptor.capture(), eq(MqTagEnum.TRANSPORT_DELIVERED.getTag()), any());
        verify(mqProducer, times(1)).sendEntityMsg(changeMqCaptor.capture(), eq(MqTagEnum.TRANSPORT_TRACK_CHANGE.getTag()), any());
        verify(orderTransportMapper, times(1)).updateStatusById(any(), any());
        TransportTrackChangeMqDTO value = changeMqCaptor.getValue();
        Assert.assertTrue(value.getChangeEvents().size() == 1);
        Assert.assertTrue(DateUtil.parseLocalDateTime(value.getChangeEvents().get(0).getDate()).isAfter(DateUtil.parseLocalDateTime(LAST_ANALYZE_TIME)));
    }

    @Test
    public void test_ewe_trackChange() {
        List<OrderTransportPO> poList = new ArrayList<>();
        OrderTransportPO po1 = OrderTransportPO.builder()
            .id(ORDER_TRANSPORT_PO_ID)
            .articleId(ARTICLE_ID_A)
            .orderNumber(ORDER_NUMBER_A)
            .status(DISPATCH_SHIPMENTS.getCode())
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .forwarderStatus("In transit")
            .deleteFlag(0)
            .createdTime(new Date())
            .updatedTime(new Date())
            .build();
        poList.add(po1);

        OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO = new OrderTraceAnalyzeLogPO()
            .setAnalyzeTime(LAST_ANALYZE_TIME)
            .setOrderNumber(ORDER_NUMBER_A)
            .setForwarderType(ForwarderTypeEnum.EWE.getName());

        String s = "";
        EweShippingStatusRespDTO statusRespDTO = JSON.parseObject(s, EweShippingStatusRespDTO.class);
        String s1 = "{\"trackingResults\":[{\"articleId\":\"RCM473878601000935009\",\"orderNumber\":\"F00000000162431\",\"status\":\"In transit\",\"events\":[{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-02 11:01:49\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-02 11:01:48\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-02 10:04:12\"},{\"desc\":\"In transit to next facility in MULGRAVE VIC\",\"date\":\"2023-08-01 00:19:16\"},{\"location\":\"SUNSHINE WEST VIC\",\"desc\":\"Item processed at facility\",\"date\":\"2023-07-31 21:55:29\"},{\"desc\":\"Received and ready for processing\",\"date\":\"2023-07-31 21:25:29\"},{\"desc\":\"Shipping information approved by Australia Post\",\"date\":\"2023-07-31 02:41:02\"},{\"desc\":\"Shipping information received by Australia Post\",\"date\":\"2023-07-29 14:51:43\"},{\"location\":\"Sydney,NSW\",\"desc\":\"Shipment information received\",\"date\":\"2023-07-28 20:47:36\"}]}]}";
        TransportTrackRespVO respVO = JSON.parseObject(s1, TransportTrackRespVO.class);

        ReflectionTestUtils.setField(eweTransportSyncWorker, "partitionSize", 10);
        ReflectionTestUtils.setField(eweTransportSyncWorker, "timeSleepMilliseconds", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryOrderPartitionSize", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "taskPartitionSize", 10);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryRemoteRetryTime", 3);
        when(transportSyncWorkerFactory.getWorker(any())).thenReturn(eweTransportSyncWorker);
        when(orderTransportMapper.selectIdByStatusListAndCarrier(any(), any())).thenReturn(Lists.newArrayList(1L));
        when(orderTransportMapper.selectAllByIds(any())).thenReturn(poList);
        when(orderTraceAnalyzeLogMapper.selectByOrderNumbersAndCarrier(any(), any())).thenReturn(Lists.newArrayList(orderTraceAnalyzeLogPO));
        when(eweRemoteService.shippingStatus(any())).thenReturn(statusRespDTO);
        when(eweTransportTrackStruct.toVO(any())).thenReturn(respVO);
        ArgumentCaptor<TransportTrackChangeMqDTO> changeMqCaptor = ArgumentCaptor.forClass(TransportTrackChangeMqDTO.class);

        transportTraceSyncJob.processByCarrier(CarrierTypeEnum.AUS_POST.getName());

        verify(mqProducer, never()).sendEntityMsg(any(), eq(MqTagEnum.TRANSPORT_DELIVERED.getTag()), any());
        verify(mqProducer, times(1)).sendEntityMsg(changeMqCaptor.capture(), eq(MqTagEnum.TRANSPORT_TRACK_CHANGE.getTag()), any());
        verify(orderTransportMapper, never()).updateStatusById(any(), any());
        TransportTrackChangeMqDTO value = changeMqCaptor.getValue();
        Assert.assertTrue(value.getChangeEvents().size() == 3);
        Assert.assertTrue(DateUtil.parseLocalDateTime(value.getChangeEvents().get(2).getDate()).isAfter(DateUtil.parseLocalDateTime(LAST_ANALYZE_TIME)));
    }

    @Test
    public void test_byOrder() {
        List<OrderTransportPO> poList = new ArrayList<>();
        OrderTransportPO po1 = OrderTransportPO.builder()
            .id(ORDER_TRANSPORT_PO_ID)
            .articleId(ARTICLE_ID_A)
            .orderNumber(ORDER_NUMBER_A)
            .status(DISPATCH_SHIPMENTS.getCode())
            .forwarderType(ForwarderTypeEnum.EWE.getName())
            .carrier(CarrierTypeEnum.AUS_POST.getName())
            .forwarderStatus("In transit")
            .deleteFlag(0)
            .createdTime(new Date())
            .updatedTime(new Date())
            .build();
        poList.add(po1);
        OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO = new OrderTraceAnalyzeLogPO()
            .setAnalyzeTime(LAST_ANALYZE_TIME)
            .setOrderNumber(ORDER_NUMBER_A)
            .setForwarderType(ForwarderTypeEnum.EWE.getName());

        String s = "";
        EweShippingStatusRespDTO statusRespDTO = JSON.parseObject(s, EweShippingStatusRespDTO.class);
        String s1 = "{\"trackingResults\":[{\"articleId\":\"RCM473878601000935009\",\"orderNumber\":\"F00000000162431\",\"status\":\"In transit\",\"events\":[{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-02 11:01:49\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-02 11:01:48\"},{\"location\":\"CLAYTON VIC\",\"desc\":\"In transit\",\"date\":\"2023-08-02 10:04:12\"},{\"desc\":\"In transit to next facility in MULGRAVE VIC\",\"date\":\"2023-08-01 00:19:16\"},{\"location\":\"SUNSHINE WEST VIC\",\"desc\":\"Item processed at facility\",\"date\":\"2023-07-31 21:55:29\"},{\"desc\":\"Received and ready for processing\",\"date\":\"2023-07-31 21:25:29\"},{\"desc\":\"Shipping information approved by Australia Post\",\"date\":\"2023-07-31 02:41:02\"},{\"desc\":\"Shipping information received by Australia Post\",\"date\":\"2023-07-29 14:51:43\"},{\"location\":\"Sydney,NSW\",\"desc\":\"Shipment information received\",\"date\":\"2023-07-28 20:47:36\"}]}]}";
        TransportTrackRespVO respVO = JSON.parseObject(s1, TransportTrackRespVO.class);

        ReflectionTestUtils.setField(eweTransportSyncWorker, "partitionSize", 10);
        ReflectionTestUtils.setField(eweTransportSyncWorker, "timeSleepMilliseconds", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryOrderPartitionSize", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "taskPartitionSize", 10);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryRemoteRetryTime", 3);
        when(transportSyncWorkerFactory.getWorker(any())).thenReturn(eweTransportSyncWorker);
        when(orderTransportMapper.queryOrderTransportList(any())).thenReturn(poList);
        when(orderTraceAnalyzeLogMapper.selectByOrderNumbersAndCarrier(any(), any())).thenReturn(Lists.newArrayList(orderTraceAnalyzeLogPO));
        when(eweRemoteService.shippingStatus(any())).thenReturn(statusRespDTO);
        when(eweTransportTrackStruct.toVO(any())).thenReturn(respVO);
        ArgumentCaptor<TransportTrackChangeMqDTO> changeMqCaptor = ArgumentCaptor.forClass(TransportTrackChangeMqDTO.class);

        transportTraceSyncJob.retryByOrders(Lists.newArrayList(ORDER_NUMBER_A));

        verify(mqProducer, never()).sendEntityMsg(any(), eq(MqTagEnum.TRANSPORT_DELIVERED.getTag()), any());
        verify(mqProducer, times(1)).sendEntityMsg(changeMqCaptor.capture(), eq(MqTagEnum.TRANSPORT_TRACK_CHANGE.getTag()), any());
        verify(orderTransportMapper, never()).updateStatusById(any(), any());
        TransportTrackChangeMqDTO value = changeMqCaptor.getValue();
        Assert.assertTrue(value.getChangeEvents().size() == 3);
        Assert.assertTrue(DateUtil.parseLocalDateTime(value.getChangeEvents().get(2).getDate()).isAfter(DateUtil.parseLocalDateTime(LAST_ANALYZE_TIME)));
    }


    @Test
    public void test_wiseway_delivered() {
        List<OrderTransportPO> poList = new ArrayList<>();
        OrderTransportPO po1 = OrderTransportPO.builder()
            .id(ORDER_TRANSPORT_PO_ID)
            .articleId(ARTICLE_ID_A)
            .orderNumber(ORDER_NUMBER_A)
            .status(DISPATCH_SHIPMENTS.getCode())
            .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
            .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
            .forwarderStatus("In transit")
            .deleteFlag(0)
            .createdTime(new Date())
            .updatedTime(new Date())
            .build();
        // 查询失败的运单
        OrderTransportPO po2 = OrderTransportPO.builder()
            .id(ORDER_TRANSPORT_PO_ID_QUERY_FAIL)
            .articleId(ARTICLE_ID_A)
            .orderNumber(ORDER_NUMBER_QUERY_FAIL)
            .status(DISPATCH_SHIPMENTS.getCode())
            .forwarderType(ForwarderTypeEnum.WISE_WAY.getName())
            .carrier(CarrierTypeEnum.AUS_POST_WISE_WAY.getName())
            .forwarderStatus("In transit")
            .deleteFlag(0)
            .createdTime(new Date())
            .updatedTime(new Date())
            .build();
        poList.add(po1);
        poList.add(po2);

        OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO = new OrderTraceAnalyzeLogPO()
            .setAnalyzeTime(LAST_ANALYZE_TIME)
            .setOrderNumber(ORDER_NUMBER_A)
            .setForwarderType(ForwarderTypeEnum.EWE.getName());
        OrderTraceAnalyzeLogPO orderTraceAnalyzeLogPO1 = new OrderTraceAnalyzeLogPO()
            .setAnalyzeTime(LAST_ANALYZE_TIME)
            .setOrderNumber(ORDER_NUMBER_QUERY_FAIL)
            .setForwarderType(ForwarderTypeEnum.EWE.getName());

        String s = "[{}]";
        List<WiseWayOrderTrackRespVO>  statusRespDTO = JSON.parseObject(s, new TypeReference<List<WiseWayOrderTrackRespVO>>(){});
        String s1 = "{\"trackingResults\":[{\"articleId\":\"RCM473878601000935009\",\"orderNumber\":\"F00000000162431\",\"events\":[{\"desc\":\"Delivered to post office box\",\"date\":\"2023-08-02 07:03:13\",\"eventCode\":40600},{\"desc\":\"in transit.\",\"date\":\"2023-08-02 05:03:13\"},{\"desc\":\"Your parcel is currently in transit.\",\"date\":\"2023-08-02 04:03:13\",\"eventCode\":40300}]},{\"articleId\":\"F00000000162432\"}]}";
        TransportTrackRespVO respVO = JSON.parseObject(s1, TransportTrackRespVO.class);

        ReflectionTestUtils.setField(wisewayTransportTrackWorker, "partitionSize", 10);
        ReflectionTestUtils.setField(wisewayTransportTrackWorker, "timeSleepMilliseconds", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryOrderPartitionSize", 200);
        ReflectionTestUtils.setField(transportTraceSyncJob, "taskPartitionSize", 10);
        ReflectionTestUtils.setField(transportTraceSyncJob, "queryRemoteRetryTime", 3);
        when(transportSyncWorkerFactory.getWorker(any())).thenReturn(wisewayTransportTrackWorker);
        when(orderTransportMapper.selectIdByStatusListAndCarrier(any(), any())).thenReturn(Lists.newArrayList(1L, 2L));
        when(orderTransportMapper.selectAllByIds(any())).thenReturn(poList);
        when(orderTraceAnalyzeLogMapper.selectByOrderNumbersAndCarrier(any(), any())).thenReturn(Lists.newArrayList(orderTraceAnalyzeLogPO, orderTraceAnalyzeLogPO1));
        when(wiseWayService.queryOrderTrack(any())).thenReturn(statusRespDTO);
        when(wiseWayTransportTrackStruct.toTrackResult(any(), anyMap())).thenReturn(respVO.getTrackingResults().get(0));
        ArgumentCaptor<TransportTrackChangeMqDTO> changeMqCaptor = ArgumentCaptor.forClass(TransportTrackChangeMqDTO.class);
        ArgumentCaptor<TransportDeliveredMqDTO> deliveredMqCaptor = ArgumentCaptor.forClass(TransportDeliveredMqDTO.class);

        transportTraceSyncJob.processByCarrier(CarrierTypeEnum.AUS_POST.getName());

        verify(mqProducer, times(1)).sendEntityMsg(deliveredMqCaptor.capture(), eq(MqTagEnum.TRANSPORT_DELIVERED.getTag()), any());
        verify(mqProducer, times(1)).sendEntityMsg(changeMqCaptor.capture(), eq(MqTagEnum.TRANSPORT_TRACK_CHANGE.getTag()), any());
        verify(orderTransportMapper, times(1)).updateStatusById(any(), any());
        TransportTrackChangeMqDTO value = changeMqCaptor.getValue();
        Assert.assertTrue(value.getChangeEvents().size() == 3);
        Assert.assertTrue(DateUtil.parseLocalDateTime(value.getChangeEvents().get(0).getDate()).isAfter(DateUtil.parseLocalDateTime(LAST_ANALYZE_TIME)));
    }

}
