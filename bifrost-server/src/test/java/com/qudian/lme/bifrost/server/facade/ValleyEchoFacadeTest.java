package com.qudian.lme.bifrost.server.facade;

import com.qudian.java.components.common.dto.BaseResponseDTO;
import com.qudian.lme.bifrost.api.facade.ValleyEchosFacade;
import com.qudian.lme.bifrost.api.vo.request.ValleyEchosReqVO;
import com.qudian.lme.bifrost.infrastructure.annotation.DubboReference;
import com.qudian.lme.bifrost.server.StartTestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

/**
 * <p>文件名称:com.qudian.lme.bifrost.server.facade.ValleyEchoFacadeTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/7/25
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class ValleyEchoFacadeTest extends StartTestApplication {
    @DubboReference
    private ValleyEchosFacade valleyEchosFacade;

    @Test
    public void echo() {
        ValleyEchosReqVO reqVO = new ValleyEchosReqVO();
        reqVO.setHeyRoar("revolution");
        BaseResponseDTO<ValleyEchosReqVO> echo = valleyEchosFacade.echo(reqVO);

        ValleyEchosReqVO res = new ValleyEchosReqVO();
        res.setHeyRoar("[Server_echo] hi:" + reqVO.getHeyRoar());
        assertThat(echo.toString()).isNotNull().isEqualTo("{\"code\":0,\"message\":\"SUCCESS\",\"displayable\":false,\"data\":\"ValleyEchosReqVO(heyRoar=[Server_echo] hi:revolution, origin=null)\"}");
    }

    @Test
    public void  test2(){
        Optional<String> a = Optional.empty();
        System.out.println(a.orElse("2222"));
    }
}
