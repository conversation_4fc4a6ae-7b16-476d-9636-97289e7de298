<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qudian.java</groupId>
        <artifactId>parent-dependencies</artifactId>
        <version>1.0.3-RELEASE</version>
    </parent>
    <groupId>com.qudian.lme.bifrost</groupId>
    <artifactId>bifrost</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <component.base.version>1.4.1-RELEASE</component.base.version>
        <component.datasource.version>1.1.3-RELEASE</component.datasource.version>
        <component.common.version>1.2.6-RELEASE</component.common.version>
        <mysql.connector.version>8.0.15</mysql.connector.version>
        <mybatis-spring.version>2.1.3</mybatis-spring.version>
        <mybatis.plus.version>3.2.0</mybatis.plus.version>
        <pagehelper.version>1.2.10</pagehelper.version>
        <dubbo.version>2.7.6</dubbo.version>
        <apollo.version>1.6.0</apollo.version>
        <prometheus.version>1.8.1</prometheus.version>
        <skywalking.version>6.3.0</skywalking.version>
        <fastjson.version>1.2.69</fastjson.version>
        <guava.version>28.2-jre</guava.version>
        <log.mask.tool.verson>1.3-RELEASE</log.mask.tool.verson>
        <caffeine.version>2.8.5</caffeine.version>
        <swagger.version>2.9.2</swagger.version>
        <swagger.ui.version>1.9.0</swagger.ui.version>
        <xxljob.version>2.1.2</xxljob.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <jupiter.version>5.8.2</jupiter.version>
        <mockito.version>4.3.1</mockito.version>
        <byte-buddy.version>1.12.7</byte-buddy.version>
        <joda.version>2.9.9</joda.version>
        <jmh.version>1.35</jmh.version>
        <javafaker.version>1.0.2</javafaker.version>
        <hutool.version>5.8.9</hutool.version>
<!--        <bifrost.api.version>1.0.8-SNAPSHOT</bifrost.api.version>-->
<!--        测试环境请注释掉正式的版本，用快照版本开发-->
        <bifrost.api.version>1.1.12-SNAPSHOT</bifrost.api.version>
        <bifrost.base.version>1.0.0-SNAPSHOT</bifrost.base.version>
        <verbal.expressions.version>1.8</verbal.expressions.version>
        <vavr.version>0.10.4</vavr.version>
        <apache.common-collections4.version>4.4</apache.common-collections4.version>
        <component.table-log.version>1.0.6-RELEASE</component.table-log.version>
        <commons-io.version>2.7</commons-io.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <retrofit.version>2.9.0</retrofit.version>
        <okhttp3.version>3.14.9</okhttp3.version>
        <rocketmq.starter.version>2.2.2</rocketmq.starter.version>
        <spring-cloud-context>3.1.0</spring-cloud-context>
        <oms.api.version>1.1.52-RELEASE</oms.api.version>
        <pay.settle.api.version>1.2.14-RELEASE</pay.settle.api.version>
        <swagger-core.version>1.6.11</swagger-core.version>
        <swagger-models.version>1.6.11</swagger-models.version>
        <swagger-annotations.version>1.6.11</swagger-annotations.version>
        <swagger-dubbo.version>2.0.3-RELEASE</swagger-dubbo.version>
        <knife4j.version>2.0.9</knife4j.version>
        <reactor-test.version>3.4.18</reactor-test.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.qudian.lme.bifrost</groupId>
                <artifactId>bifrost-api</artifactId>
                <version>${bifrost.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.lme.bifrost</groupId>
                <artifactId>bifrost-common</artifactId>
                <version>${bifrost.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.lme.bifrost</groupId>
                <artifactId>bifrost-infrastructure</artifactId>
                <version>${bifrost.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.lme.bifrost</groupId>
                <artifactId>bifrost-application</artifactId>
                <version>${bifrost.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.lme.bifrost</groupId>
                <artifactId>bifrost-kernel</artifactId>
                <version>${bifrost.base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr</artifactId>
                <version>${vavr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-core</artifactId>
                <version>${jmh.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-generator-annprocess</artifactId>
                <version>${jmh.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>${javafaker.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--基础框架依赖 -->
            <dependency>
                <groupId>com.qudian.java.components</groupId>
                <artifactId>base</artifactId>
                <version>${component.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.java.components</groupId>
                <artifactId>common</artifactId>
                <version>${component.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.java.components</groupId>
                <artifactId>datasource</artifactId>
                <version>${component.datasource.version}</version>
            </dependency>
            <!-- dubbo start -->
            <!-- dubbo-spring-boot-starter -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <!--zookeeper 注册中心客户端引入 curator客户端 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>
            <!-- dubbo end -->
            <!-- 日志脱敏 -->
            <dependency>
                <groupId>com.qudian.pay.common</groupId>
                <artifactId>log-mask-tool</artifactId>
                <version>${log.mask.tool.verson}</version>
            </dependency>
            <!--apollo-->
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis.spring.boot</groupId>
                        <artifactId>mybatis-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 工具类-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${apache.common-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxljob.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.java.components</groupId>
                <artifactId>table-log</artifactId>
                <version>${component.table-log.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>ru.lanwen.verbalregex</groupId>
                <artifactId>java-verbal-expressions</artifactId>
                <version>${verbal.expressions.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok.mapstruct.binding.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte-buddy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <!--MQ-->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>${spring-cloud-context}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.lme.oms</groupId>
                <artifactId>oms-api</artifactId>
                <version>1.1.52-RELEASE</version>
            </dependency>

            <dependency>
                <artifactId>pay-settle-api</artifactId>
                <groupId>com.qudian.pay</groupId>
                <version>${pay.settle.api.version}</version>
            </dependency>
            <dependency>
                <artifactId>channel-ms-api</artifactId>
                <groupId>com.qudian.channelms</groupId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-test</artifactId>
                <version>${reactor-test.version}</version>
                <scope>test</scope>
            </dependency>


            <!-- swagger-duubo依赖 -->
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>swagger-dubbo</artifactId>
                <version>${swagger-dubbo.version}</version>
            </dependency>
            <!-- swagger-duubo ui依赖 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!--多环境配置begin-->
    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profilesActive>local</profilesActive>
                <dockerTag>local</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profilesActive>dev</profilesActive>
                <dockerTag>dev</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profilesActive>test</profilesActive>
                <dockerTag>test</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>test2</id>
            <properties>
                <profilesActive>test2</profilesActive>
                <dockerTag>test2</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profilesActive>prod</profilesActive>
                <dockerTag>latest</dockerTag>
                <hurbo/>
            </properties>
        </profile>
    </profiles>
    <!--多环境配置end-->
    <distributionManagement>
        <repository>
            <!-- ID要和MAVEN中conif/setting.xml 中的server保持一致 -->
            <id>releases</id>
            <name>User Project Release</name>
            <!-- release版本的url地址 -->
            <url>http://***********:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>User Project SNAPSHOTS</name>
            <url>http://***********:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- docker -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgument>-parameters</compilerArgument>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <!-- due to problem in maven-compiler-plugin, for verbose mode add showWarnings -->
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>
                            -Amapstruct.defaultComponentModel=spring
                        </arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <includeEmptyDirs>true</includeEmptyDirs>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>public</id>
            <name>public repository</name>
            <url>http://***********:8081/repository/maven-public/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>snapshots repository</name>
            <url>http://***********:8081/repository/maven-snapshots/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
        <repository>
            <id>releases</id>
            <name>releases repository</name>
            <url>http://***********:8081/repository/maven-releases/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>
    <!--maven仓库配置end-->

<modules>
    <module>bifrost-infrastructure</module>
    <module>bifrost-server</module>
    <module>bifrost-common</module>
    <module>bifrost-api</module>
    <module>bifrost-application</module>
    <module>bifrost-kernel</module>
</modules>
</project>
