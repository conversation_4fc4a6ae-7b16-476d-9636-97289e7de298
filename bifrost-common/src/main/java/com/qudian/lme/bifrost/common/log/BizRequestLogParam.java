package com.qudian.lme.bifrost.common.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * {@inheritDoc}
 *
 * <AUTHOR>
 * @since 2023/9/7
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class BizRequestLogParam {

    /**
     * 转运服务商类型
     */
    private String forwarderType;

    /**
     * 运单id
     */
    private List<String> orderNumbers;

    /**
     * 承运商
     */
    private String carrier;

}
