package com.qudian.lme.bifrost.common.utils.common;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.util.CollectionUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by tinder1024 on 2017/6/15.
 */
public class Utils {
    private static final Pattern ARRAY_PATTERN = Pattern.compile("(\\w+)\\[(\\d+)\\]");
    private static final Pattern ARRAY_FILTER_PATTERN = Pattern.compile("(\\w+)\\[(\\w+)=(\\w+)\\]");
    public interface ReplaceCallback{
        String replace(String text, int index, Matcher matcher);
    }

    /**
	 * 参考另一个同名方法，这个方法封装了一个泛型，将返回值转换为指定泛型
     * @param obj 目标对象
     * @param cls 获得的值的class
     * @param <T>
     * @return
     */
    public static <T> T getByKey(Object obj,String key,Class<T> cls){
        Object r = getByKey(obj,key);
        if(r == null)return null;
        if(cls.getSimpleName().equals("Float") && r.getClass().getSimpleName().equals("Double")){
            double d = (double)r;
            Float f = new Float(d);
            return (T)f;
        }
        return (T)r;
    }

    /**
     * 参考另一个同名方法，这个方法封装了一个泛型，将返回值转换为指定泛型
     * @param obj 目标对象
     * @return
     */
    public static Integer getByKeyToInteger(Object obj,String key){
        Object r = getByKey(obj,key);
        if(r == null)return null;
        return Integer.valueOf(r.toString());
    }

    /**
     * 参考另一个同名方法，这个方法封装了一个泛型，将返回值转换为指定泛型
     * @param obj 目标对象
     * @return
     */
    public static Long getByKeyToLong(Object obj,String key){
        Object r = getByKey(obj,key);
        if(r == null)return null;
        return Long.valueOf(r.toString());
    }


    public static String toString(Object obj){
        if(obj == null)return "null";
        return obj.toString();
    }

    /**
     * 通过类似json path的方式，从一个对象中反射获取值
     * 这个接口返回object
     * @param obj
     * @param key "a.b" "a.b[0]" "a.b[c=x]"
     *            "a.b" 表示取obj中a属性下的b属性
     *            "a.b[0]" 表示取obj中a属性下b这个数组的第0项
     *            "a.b[c=x]" 表示取obj中a属性下b这个数组中包含了属性c的值为x的对象
     * @return
     */
    public static Object getByKey(Object obj,String key){
        Object r = obj;
        if(obj == null){
            throw new NullPointerException("obj can not be null");
        }
        if(key == null){
            throw new NullPointerException("key can not be null");
        }
        String[] keySp = key.split("\\.");
        for(String k:keySp){
            int isList = -1;
            String filterKey = null;
            String filterVal = null;
            if(k.indexOf("[")>0){
                Matcher matcher = ARRAY_PATTERN.matcher(k);
                if(matcher.matches()){
                    k = matcher.group(1);
                    isList = Integer.parseInt(matcher.group(2));
                }
                else{
                    Matcher filterMatcher = ARRAY_FILTER_PATTERN.matcher(k);
                    if(filterMatcher.matches()){
                        k = filterMatcher.group(1);
                        filterKey = filterMatcher.group(2);
                        filterVal = filterMatcher.group(3);
                    }
                }
            }
            r = getByField(r,k);
            if(r == null){
                return null;
            }
            if(isList > -1){
                if(r instanceof List){
                    List listR = (List)r;
                    r = listR.get(isList);
                }
                else{
                    return null;
                }
            }
            else if(filterKey != null){
                if(r instanceof List){
                    List listR = (List) r;
                    boolean match = false;
                    for(Object item:listR){
                        Object itemVal = getByField(item,filterKey);
                        if(itemVal != null && itemVal.toString().equals(filterVal)){
                            match = true;
                            r = item;
                            break;
                        }
                    }
                    if(!match){
                        return null;
                    }
                }
                else{
                    return null;
                }
            }
        }
        return r;
    }

    private static Object getByField(Object obj,String key){
        if(obj == null || key == null)return null;
        if(obj instanceof Map){
            Map m = (Map)obj;
            return m.get(key);
        }
        else{
            try {
                return obj.getClass().getField(key).get(obj);
            } catch (Exception e) {
            }
            if(key.length() > 1){
                String methodName = String.format("get%s%s",key.substring(0,1).toUpperCase(),key.substring(1));
                try {
                    Method method = obj.getClass().getMethod(methodName);
                    return method.invoke(obj);
                } catch (Exception e) {
                }
            }
            return null;
        }
    }

    /**
     * 随机生成国内IP地址
     */
    public static String getRandomIp() {

        // ip范围
        int[][] range = { { 607649792, 608174079 },// *********-*************
                { 1038614528, 1039007743 },// **********-**************
                { 1783627776, 1784676351 },// **********-**************
                { 2035023872, 2035154943 },// **********-**************
                { 2078801920, 2079064063 },// ***********-***************
                { -1950089216, -1948778497 },// ***********-***************
                { -1425539072, -1425014785 },// *********-**************
                { -1236271104, -1235419137 },// **********-**************
                { -770113536, -768606209 },// **********-**************
                { -569376768, -564133889 }, // **********-**************
        };

        Random rdint = new Random();
        int index = rdint.nextInt(10);
        String ip = num2ip(range[index][0] + new Random().nextInt(range[index][1] - range[index][0]));
        return ip;
    }

    /**
     * 将十进制转换成ip地址
     */
    public static String num2ip(int ip) {
        int[] b = new int[4];
        String x = "";

        b[0] = (int) ((ip >> 24) & 0xff);
        b[1] = (int) ((ip >> 16) & 0xff);
        b[2] = (int) ((ip >> 8) & 0xff);
        b[3] = (int) (ip & 0xff);
        x = Integer.toString(b[0]) + "." + Integer.toString(b[1]) + "." + Integer.toString(b[2]) + "." + Integer.toString(b[3]);

        return x;
    }

    /**
     * 支持通过代码逻辑进行字符串替换
     * @param str 要替换的字符串
     * @param pattern 正则表达式
     * @param callback 执行替换逻辑的ReplaceCallback示例，参考内部接口ReplaceCallback
     * @return 替换后的字符串
     */
    public static String replaceAll(String str,Pattern pattern,ReplaceCallback callback){
        if (str == null) {
            return null;
        }
        Matcher m = pattern.matcher(str);
        if (m.find()) {
            StringBuffer sb = new StringBuffer();
            int index = 0;
            while (true) {
                m.appendReplacement(sb, callback.replace(m.group(0), index++, m));
                if (!m.find()) {
                    break;
                }
            }
            m.appendTail(sb);
            return sb.toString();
        }
        return str;
    }

    public static String listJoin(List list){
        return listJoin(list,",");
    }

    /**
     * 将list中的元素toString后，用指定分隔符连接成字符串
     * @param list
     * @param sep
     * @return
     */
    public static String listJoin(List list,String sep){
        if(list == null)return "";
        if(list.size() == 1){
            Object item = list.get(0);
            return item == null?"null":item.toString();
        }
        if(sep == null){
            sep = ",";
        }
        StringBuilder sb = new StringBuilder();
        boolean isFirst = true;
        for(Object item:list){
            if(isFirst){
                isFirst = false;
            }
            else {
                sb.append(sep);
            }
            sb.append(item);
        }
        return sb.toString();
    }


    /**
     * 获取日期 i为正数 向后推迟i天，负数时向前提前i天
     * @param i
     * @param format
     * @return
     */
    public static String getDate(int i, String format)
    {
        if(StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        calendar.add(Calendar.DATE, i);
        String timeAgo = sdf.format(calendar.getTime());
        return timeAgo;
    }

    // 加密
    public static String encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            System.out.print("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            System.out.print("Key长度不是16位");
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        return new String(Base64.encodeBase64(encrypted), "utf-8");
      //  return new Base64().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    /**
     * 获取TRACE信息
     * <AUTHOR>
     * @date 2019-12-25
    */
    public static Map<String, String> getTraceHeader(Map<String, String> threadContextMap) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("X-TRACE-ID", threadContextMap.get("X-TRACE-ID"));
        headerMap.put("X-PARENT-ID", threadContextMap.get("X-PARENT-ID"));
        headerMap.put("X-PARENT-NAME", threadContextMap.get("X-PARENT-NAME"));
        headerMap.put("SKY-TRACE-ID", threadContextMap.get("SKY-TRACE-ID"));
        return headerMap;
    }

    /**
     * 判断字符串是否在数组中
     * @param stringArray 要查找的字符串数组
     * @param string 要查找的字符或字符串
     * @return
     */
    public static Boolean arrayIsHaveString(String[] stringArray, String string) {
        if(stringArray == null || string == null) {
            return false;
        }

        for(int i=0; i<stringArray.length; i++) {
            if(stringArray[i].equals(string)) {
                return true;
            }
        }

        return false;
    }

    public static <T> boolean listEquals(List<T> list1, List<T> list2) {
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return false;
        }
        if (list1.size() == list2.size() && list1.containsAll(list2) && list2.containsAll(list1)) {
            return true;
        }
        return false;
    }

    public static List<String> splitLength(String sourceString, int length) {
        List<String> stringList = new ArrayList<>();
        if (sourceString == null || "".equals(sourceString)) {
            return stringList;
        }

        if (length <= 0) {
            stringList.add(sourceString);
            return stringList;
        }
        String tempString = sourceString;
        int beginIndex = 0;
        int endIndex = length;
        for (int i = 0; i < Math.floorDiv(sourceString.length() , length) + 1; i++) {
            if (tempString.length() < length) {
                endIndex = tempString.length();
                stringList.add(tempString.substring(beginIndex, endIndex));
            } else {
                stringList.add(tempString.substring(beginIndex, endIndex));
                tempString = tempString.substring(endIndex);
            }
        }
        return stringList;
    }

    public static String ignoreExpression(String inputStr) {
        if (StringUtils.isBlank(inputStr)) {
            return inputStr;
        }
        return inputStr.replaceAll("[^\\u0000-\\uFFFF]", "");

    }

}
