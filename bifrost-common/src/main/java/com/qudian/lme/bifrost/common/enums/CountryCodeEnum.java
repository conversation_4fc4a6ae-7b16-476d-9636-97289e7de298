package com.qudian.lme.bifrost.common.enums;

import com.qudian.java.components.base.constant.TimeZoneEnum;
import com.qudian.lme.bifrost.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * <p>文件名称:com.qudian.lme.driver.common.enums</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">kangjun</a>
 * @version 1.0
 * @since 2022/12/1 2:09 下午
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum CountryCodeEnum {
    AUSTRALIA("au", TimeZoneEnum.AET),
    SPAIN("es", TimeZoneEnum.EM),
    AMERICA("us", TimeZoneEnum.CST),
    NEW_ZEALAND("nz", TimeZoneEnum.NST),
    CANADA("ca", TimeZoneEnum.AST)
    ,;

    private final String code;
    private final TimeZoneEnum defaultTimeZone;

    public static CountryCodeEnum getCountryByCode(String code, CountryCodeEnum defaultCountry) {
        CountryCodeEnum countryCodeEnum = Arrays.stream(CountryCodeEnum.values()).
                filter(f -> f.code.equals(code)).findFirst().orElse(null);
        if (countryCodeEnum == null) {
            log.error("CountryCodeEnum 未匹配到国家, 使用了默认国家澳大利亚, 请人工介入");
            return defaultCountry;
        }
        return countryCodeEnum;
    }
    public static CountryCodeEnum getCountryByCode(String code) {
        return Arrays.stream(CountryCodeEnum.values()).
                filter(f -> f.code.equals(code.toLowerCase())).findFirst().orElseThrow(()->new BizException("请输入对应的code"));


    }

}

