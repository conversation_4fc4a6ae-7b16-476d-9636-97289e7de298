package com.qudian.lme.bifrost.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("lme.aus.transport")
@RefreshScope
public class AusTransportApiConfig {


    private String host;

    private String username;

    private String password;

    private String pushUnCreatedShipments;

    private String dispatchShipments;

    private String cancelShipment;

    private String modifyShipment;

    private String printLabel;

    /**
     * 查询配送状态url
     */
    private String shippingStatus;

    /**
     * 时区
     */
    private String timezone;

}
