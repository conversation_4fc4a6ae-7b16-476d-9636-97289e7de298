package com.qudian.lme.bifrost.common.config;


import com.qudian.lme.bifrost.common.utils.HttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @desc: httpClient
 * @author: lian<PERSON><PERSON>@qudian.com
 * @date: Created at 2020-11-02 15:19
 */
@Configuration
public class HttpClientConfig {

    private static final long CONNECTION_TIMEOUT = 3000;
    private static final long READ_TIMEOUT = 300000;

    @Bean
    public HttpClient httpClient() {
        return new HttpClient(CONNECTION_TIMEOUT, READ_TIMEOUT);
    }

}
