package com.qudian.lme.bifrost.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("lms.send.speed.transport")
@RefreshScope
public class SendSpeedTransportApiConfig {
    private String url;
    /**
     * api账号
     */
    private String appToken;

    /**
     * api密码
     */
    private String appKey;


    /**
     * 产品代码
     */
    private String shippingMethod;
}
