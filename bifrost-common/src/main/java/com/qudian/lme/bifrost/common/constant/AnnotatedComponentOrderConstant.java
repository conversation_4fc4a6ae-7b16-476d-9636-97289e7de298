package com.qudian.lme.bifrost.common.constant;

import org.springframework.core.Ordered;

/**
 * <p>文件名称:com.qudian.lme.driver.common.enums.AnnotatedComponentOrderEnum</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/2/10
 */
public class AnnotatedComponentOrderConstant {
    public static final int FACADE_WEAVER = Ordered.LOWEST_PRECEDENCE - 100;

}
