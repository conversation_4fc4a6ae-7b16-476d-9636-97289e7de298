package com.qudian.lme.bifrost.common.exception;

import com.qudian.java.components.common.exception.BaseException;
import com.qudian.lme.bifrost.common.enums.ExceptionEnum;

/**
 * {@inheritDoc} 限流异常
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
public class RateLimitException extends BaseException {

    private static final ExceptionEnum code = ExceptionEnum.RATE_LIMIT;

    public RateLimitException(String message) {
        super(code.getCode(), message, true);
    }
    public RateLimitException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getCode(), exceptionEnum.getMsg(), true);
    }

    public RateLimitException(ExceptionEnum exceptionEnum, String message) {
        super(code.getCode(), message, true);
    }

}
