package com.qudian.lme.bifrost.common.aspect;

import com.alibaba.fastjson.JSON;
import com.qudian.lme.bifrost.common.annotation.RateLimit;
import com.qudian.lme.bifrost.common.config.RateLimitConfig;
import com.qudian.lme.bifrost.common.exception.RateLimitException;
import com.qudian.lme.bifrost.common.limiter.RedisLimiter;
import com.qudian.lme.bifrost.common.utils.middleware.SpelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Optional;

/**
 * {@inheritDoc} redis限流器
 *
 * <AUTHOR>
 * @since 2023/7/31
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class RateLimitWeaver {

    @Resource
    private RedisLimiter redisLimiter;
    @Resource
    private RateLimitConfig rateLimitConfig;

    public static final String REDIS_RATE_LIMIT = "BIFROST_RATE_LIMIT_%s_%s";

    @Before("@annotation(limitAnnotation)")
    public void limitBeforeExecute(JoinPoint joinPoint, RateLimit limitAnnotation) {
        String name = limitAnnotation.name();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = methodSignature.getMethod();
        String url = limitAnnotation.url();

        String forwarderType = SpelUtil.parse(joinPoint.getTarget(), name, targetMethod, joinPoint.getArgs(), String.class);
        RateLimitConfig.LimitProperty limitProperty = Optional.ofNullable(rateLimitConfig.getConfig()).map(m -> m.get(forwarderType)).orElse(null);
        // 没有配置降级不做限流
        if (limitProperty == null) {
            log.error("rate limit config:{}, forwarderType:{}, rate limit config is null", JSON.toJSONString(rateLimitConfig), forwarderType);
            return;
        }
        int limit = limitProperty.getLimit();
        int perSeconds = limitProperty.getPerSeconds();
        String limitKey;
        if (StringUtils.isEmpty(url)) {
            limitKey = String.format(REDIS_RATE_LIMIT, forwarderType, targetMethod.getName());
        } else {
            String requestUrl = SpelUtil.parse(joinPoint.getTarget(), url, targetMethod, joinPoint.getArgs(), String.class);
            limitKey = String.format(REDIS_RATE_LIMIT, forwarderType, requestUrl);
        }

        if (limitKey == null || limitKey.trim().isEmpty()) {
            return;
        }

        if (redisLimiter.isOverLimit(limitKey, perSeconds, limit)) {
            log.warn("limitKey:{}, limit:{}, perSeconds:{}, over limit", limitKey, limit, perSeconds);
            throw new RateLimitException("接口流量超限");
        }
    }

}
