package com.qudian.lme.bifrost.common.log.annotion;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * {@inheritDoc} 请求日志记录
 *
 * <AUTHOR>
 * @since 2023/7/31
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface RequestLog {

    /**
     * 接口URL
     */
    String requestUrl() default "";

    /**
     * 请求body
     */
    String requestBody() default "";

    /**
     * 业务参数
     */
    String bizParam() default "";
}
