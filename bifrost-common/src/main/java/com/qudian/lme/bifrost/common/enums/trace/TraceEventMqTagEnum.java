package com.qudian.lme.bifrost.common.enums.trace;

import lombok.AllArgsConstructor;

/**
 * <p>文件名称:com.qudian.lme.bifrost.common.enums.trace.TraceEventMqTagEnum</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/28
 */
@AllArgsConstructor
public enum TraceEventMqTagEnum {
    DELIVERED("delivered")
    ,;
    public final String tag;
}
