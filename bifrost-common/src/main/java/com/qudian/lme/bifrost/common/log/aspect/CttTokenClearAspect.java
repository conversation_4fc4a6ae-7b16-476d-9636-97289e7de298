package com.qudian.lme.bifrost.common.log.aspect;

import com.qudian.lme.bifrost.common.dto.HttpRespDTO;
import com.qudian.lme.bifrost.common.exception.BizException;
import com.qudian.lme.bifrost.common.log.annotion.TokenClearWhenError;
import com.qudian.lme.bifrost.common.utils.common.RedisOperateUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Aspect
@Slf4j
@Component
public class CttTokenClearAspect {
    @Resource
    private RedisOperateUtil redisOperateUtil;
    @Pointcut(value = "@annotation(tokenClearWhenError)")
    private void pointcut(TokenClearWhenError tokenClearWhenError) {
    }
    @AfterReturning(value = "pointcut(tokenClearWhenError)", returning = "result")
    public void methodAfterReturning(JoinPoint joinPoint, Object result, TokenClearWhenError tokenClearWhenError) {
        if (result instanceof HttpRespDTO){
            HttpRespDTO res = (HttpRespDTO) result;
            Integer code = res.getCode();
            if (code==401){
                redisOperateUtil.delete(tokenClearWhenError.tokenKey());
                log.error("{}",res);
                throw new BizException("获取第三方token异常，请重试");
            }
            
        }
    }

    /**
     * 异常通知：目标方法抛出异常后触发
     */
    @AfterThrowing(value = "pointcut(tokenClearWhenError)", throwing="ex")
    public void methodExceptionOccurred(JoinPoint joinPoint, Exception ex,TokenClearWhenError tokenClearWhenError) {
        redisOperateUtil.delete(tokenClearWhenError.tokenKey());
    }
}
