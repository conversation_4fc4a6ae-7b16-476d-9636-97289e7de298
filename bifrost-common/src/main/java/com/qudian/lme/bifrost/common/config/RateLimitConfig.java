package com.qudian.lme.bifrost.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * {@inheritDoc} redis限流器配置
 *
 * <AUTHOR>
 * @since 2023/8/1
 **/
@Data
@Configuration
@ConfigurationProperties("lme.rate.limit")
@RefreshScope
public class RateLimitConfig {

    /**
     * 限流配置, key=服务商类型，value为限流配置项
     */
    private Map<String, LimitProperty> config;

    @Data
    public static class LimitProperty {
        /**
         * 限流数量
         */
        private Integer limit;
        /**
         * 限流时间窗口
         */
        private Integer perSeconds;
    }
}
