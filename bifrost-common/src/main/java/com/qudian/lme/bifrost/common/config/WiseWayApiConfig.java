package com.qudian.lme.bifrost.common.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("lms.wisway.transport")
@RefreshScope
public class WiseWayApiConfig {
    private String host;

    private String appkey;

    private String secret;
}
