package com.qudian.lme.bifrost.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Huang
 * @date 2025/8/27
 */
@Getter
@AllArgsConstructor
public enum EnvironmentEnum {
    /**
     * local环境
     */
    ENV_LOCAL("local"),
    /**
     * test环境
     */
    ENV_TEST("test"),
    /**
     * dev环境
     */
    ENV_DEV("dev"),
    /**
     * 生产环境
     */
    ENV_PROD("prod");
    private final String envLabel;
}
